"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_38__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Automatically expand the accordion for the newly created trip\n                setAccordionValue(trip.id.toString());\n                scrollToAccordionItem(trip.id);\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async (input)=>{\n        if (!edit_tripReport) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to add a trip\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n        setSelectedTripScheduleServiceID(null);\n        setTripReportSchedules([]);\n        setShowNextTrips(false);\n    };\n    const handleAddTrip = async ()=>{\n        const allowedVesselTypes = [\n            \"SLALL\",\n            \"Tug_Boat\",\n            \"Passenger_Ferry\",\n            \"Water_Taxi\"\n        ];\n        if (allowedVesselTypes.includes(vessel.vesselType)) {\n            loadTripScheduleServices();\n        } else {\n            handleCustomTrip();\n        }\n    };\n    const handleSelectTripReportSchedule = (trip)=>{\n        setSelectedTripReportSchedule(trip);\n        setOpenTripSelectionDialog(false);\n        const input = {\n            tripReportScheduleID: trip.id,\n            departTime: trip.departTime,\n            arriveTime: trip.arriveTime,\n            fromLocationID: trip.fromLocationID,\n            toLocationID: trip.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input);\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                                options: tripScheduleServices,\n                                value: tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                                onChange: (e)=>{\n                                    if (e) {\n                                        setSelectedTripScheduleServiceID(e.value);\n                                        loadTripReportSchedules(e.value);\n                                    } else {\n                                        setSelectedTripScheduleServiceID(null);\n                                        setTripReportSchedules([]);\n                                        setShowNextTrips(false);\n                                    }\n                                },\n                                placeholder: \"Select Trip Schedule Service\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                onClick: handleCustomTrip,\n                                variant: \"primary\",\n                                disabled: locked,\n                                children: \"Add Non-Scheduled Trip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 748,\n                        columnNumber: 17\n                    }, this),\n                    selectedTripScheduleServiceID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_20__.CheckFieldLabel, {\n                        id: \"show-next-trips\",\n                        type: \"checkbox\",\n                        checked: showNextTrips,\n                        onCheckedChange: (checked)=>{\n                            setShowNextTrips(checked);\n                            loadTripReportSchedules(selectedTripScheduleServiceID);\n                        },\n                        label: \"Show next trips\",\n                        className: \"mt-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 780,\n                        columnNumber: 21\n                    }, this),\n                    tripReportSchedules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                        headings: [\n                            \"Depart Time\",\n                            \"Depart Location\",\n                            \"Arrival Time\",\n                            \"Destination\",\n                            \"\"\n                        ],\n                        showHeader: true,\n                        className: \"mb-4\",\n                        children: tripReportSchedules.map((trs)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: trs.departTime\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: trs.fromLocation.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: trs.arriveTime\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: trs.toLocation.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            onClick: ()=>handleSelectTripReportSchedule(trs),\n                                            iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, trs.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 807,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 747,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 824,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: tripReport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedTab(0);\n                            setOpenTripModal(false);\n                            setCurrentTrip(false);\n                        } else {\n                            // If opening an accordion item, set the trip data\n                            const selectedTrip = tripReport.find((trip)=>trip.id.toString() === value);\n                            if (selectedTrip) {\n                                setSelectedTab(selectedTrip.id);\n                                setOpenTripModal(true);\n                                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                                currentTripRef.current = selectedTrip;\n                                setCurrentTrip(selectedTrip);\n                                // Initialize signature data if available\n                                if (selectedTrip.sectionSignature) {\n                                    setSignature(selectedTrip.sectionSignature.signatureData || \"\");\n                                } else {\n                                    setSignature(\"\");\n                                }\n                                setRiskBufferEvDgr(selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.dangerousGoodsChecklist);\n                                setOpenTripStartRiskAnalysis(false);\n                                setAllDangerousGoods(false);\n                                setCurrentStopEvent(false);\n                                setCurrentEventTypeEvent(false);\n                                setSelectedRowEvent(false);\n                                setDisplayDangerousGoods((selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.enableDGR) === true);\n                                setDisplayDangerousGoodsSailing((selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.designatedDangerousGoodsSailing) === true);\n                                setDisplayDangerousGoodsPvpd(false);\n                                setDisplayDangerousGoodsPvpdSailing(null);\n                                setAllPVPDDangerousGoods(false);\n                                setSelectedDGRPVPD(false);\n                                setTripReport_Stops(false);\n                            }\n                        }\n                    },\n                    children: tripReport.filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip, index)=>{\n                        var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2, _currentTripRef_current;\n                        // Generate trip display text\n                        const tripDisplayText = \"\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\").concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \")) : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \"- No arrival time \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" - \" : \" \");\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionItem, {\n                            value: trip.id.toString(),\n                            id: \"triplog-\".concat(trip.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tripDisplayText\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 926,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 41\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionContent, {\n                                    className: \"px-5 sm:px-10\",\n                                    children: currentTrip && currentTrip.id === trip.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 65\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 65\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1026,\n                                                                columnNumber: 65\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        offline: offline,\n                                                        currentTrip: currentTrip,\n                                                        tripReport: tripReport,\n                                                        templateStyle: \"\",\n                                                        updateTripReport: updateTripReport\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                                                        label: \"Departure location\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            offline: offline,\n                                                            setCurrentLocation: (location)=>{\n                                                            // Store coordinates if needed for direct coordinate input\n                                                            },\n                                                            handleLocationChange: (selectedLocation)=>{\n                                                                // Update the from location\n                                                                if (offline) {\n                                                                    updateTripReport({\n                                                                        id: [\n                                                                            ...tripReport.map((trip)=>trip.id),\n                                                                            currentTrip.id\n                                                                        ],\n                                                                        currentTripID: currentTrip.id,\n                                                                        key: \"fromLocationID\",\n                                                                        value: selectedLocation.value,\n                                                                        label: selectedLocation.label\n                                                                    });\n                                                                } else {\n                                                                    var _currentTripRef_current;\n                                                                    // For online mode, use the mutation\n                                                                    updateTripReport_LogBookEntrySection({\n                                                                        variables: {\n                                                                            input: {\n                                                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                                                fromLocationID: selectedLocation.value\n                                                                            }\n                                                                        }\n                                                                    });\n                                                                }\n                                                            },\n                                                            currentEvent: {\n                                                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                                                            },\n                                                            showAddNewLocation: true,\n                                                            showUseCoordinates: true,\n                                                            showCurrentLocation: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 65\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H5, {\n                                                        children: \"PEOPLE ON BOARD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        offline: offline,\n                                                        currentTrip: currentTrip,\n                                                        tripReport: tripReport,\n                                                        vessel: vessel,\n                                                        crewMembers: crewMembers,\n                                                        logBookConfig: logBookConfig,\n                                                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                                                        updateTripReport: updateTripReport\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                offline: offline,\n                                                                currentTrip: currentTrip,\n                                                                logBookConfig: logBookConfig\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 69\n                                                            }, this),\n                                                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                offline: offline,\n                                                                locked: locked || !edit_tripReport,\n                                                                currentTrip: currentTrip,\n                                                                logBookConfig: logBookConfig,\n                                                                selectedDGR: selectedDGR,\n                                                                members: crewMembers,\n                                                                displayDangerousGoods: displayDangerousGoods,\n                                                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                                                allDangerousGoods: allDangerousGoods,\n                                                                setAllDangerousGoods: setAllDangerousGoods\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 69\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1223,\n                                                                columnNumber: 65\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        offline: offline,\n                                                        logBookStartDate: logBookStartDate,\n                                                        currentTrip: currentTrip,\n                                                        logBookConfig: logBookConfig,\n                                                        updateTripReport: updateTripReport,\n                                                        locked: locked,\n                                                        geoLocations: locations,\n                                                        tripReport: tripReport,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        vessel: vessel,\n                                                        vessels: vessels,\n                                                        setSelectedRow: setSelectedRowEvent,\n                                                        setCurrentEventType: setCurrentEventTypeEvent,\n                                                        setCurrentStop: setCurrentStopEvent,\n                                                        currentEventType: currentEventTypeEvent,\n                                                        currentStop: currentStopEvent,\n                                                        tripReport_Stops: tripReport_Stops,\n                                                        setTripReport_Stops: setTripReport_Stops,\n                                                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                        selectedDGRPVPD: selectedDGRPVPD,\n                                                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                                        fuelLogs: fuelLogs\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                                                        label: \"Arrival location\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            offline: offline,\n                                                            setCurrentLocation: (location)=>{\n                                                            // Store coordinates if needed for direct coordinate input\n                                                            },\n                                                            handleLocationChange: (selectedLoc)=>{\n                                                                // Update the to location\n                                                                if (offline) {\n                                                                    updateTripReport({\n                                                                        id: [\n                                                                            ...tripReport.map((trip)=>trip.id),\n                                                                            currentTrip.id\n                                                                        ],\n                                                                        currentTripID: currentTrip.id,\n                                                                        key: \"toLocationID\",\n                                                                        value: selectedLoc.value,\n                                                                        label: selectedLoc.label\n                                                                    });\n                                                                } else {\n                                                                    var _currentTripRef_current;\n                                                                    // For online mode, use the mutation\n                                                                    updateTripReport_LogBookEntrySection({\n                                                                        variables: {\n                                                                            input: {\n                                                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                                                toLocationID: selectedLoc.value\n                                                                            }\n                                                                        }\n                                                                    });\n                                                                }\n                                                            },\n                                                            currentEvent: {\n                                                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                                                            },\n                                                            showAddNewLocation: true,\n                                                            showUseCoordinates: true,\n                                                            showCurrentLocation: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 65\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1389,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                setCommentField: setComment,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1415,\n                                                columnNumber: 57\n                                            }, this),\n                                            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    tripReport: tripReport,\n                                                    crewMembers: crewMembers,\n                                                    updateTripReport: updateTripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                    lineNumber: 1435,\n                                                    columnNumber: 65\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1433,\n                                                columnNumber: 61\n                                            }, this),\n                                            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                locked: locked,\n                                                title: \"Signature   Confirmation\",\n                                                description: \"By signing below, I   confirm that the   recorded entries are   accurate to the best   of my knowledge and   in accordance with   the vessel's   operating procedures   and regulations.\",\n                                                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                                                onSignatureChanged: (sign)=>{\n                                                    setSignature(sign);\n                                                }\n                                            }, \"\".concat(signatureKey, \"-\").concat(trip.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 61\n                                            }, this),\n                                            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                                                className: \"justify-end gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                        variant: \"back\",\n                                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n                                                        onClick: handleCancel,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1489,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                                                        onClick: handleSave,\n                                                        children: \"Update\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1499,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1488,\n                                                columnNumber: 61\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1005,\n                                        columnNumber: 53\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 41\n                                }, this)\n                            ]\n                        }, \"triplog-\".concat(index), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 37\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 21\n                }, this) : \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 829,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"fcxN08Zon4WnxG1OD71DL34ZJiI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        nuqs__WEBPACK_IMPORTED_MODULE_38__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation\n    ];\n});\n_c = TripLog;\nvar _c;\n$RefreshReg$(_c, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});