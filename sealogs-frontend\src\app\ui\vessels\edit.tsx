'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import {
    getInventoryList,
    getSeaLogsMembersList,
    getInventoryByVesselId,
    getInventoryCategory,
    getDepartmentList,
    getSeaLogsGroups,
} from '@/app/lib/actions'

import { CountriesList, vesselIconList } from '@/app/lib/data'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
    UPDATE_VESSEL,
    UPDATE_ENGINE,
    CREATE_ENGINE,
    CREATE_PARENT_COMPONENT,
    CREATE_FUELTANK,
    UPDATE_FUELTANK,
    CREATE_INVENTORY,
    CREATE_USER,
    UPDATE_INVENTORY,
    CREATE_WATERTANK,
    UPDATE_WATERTANK,
    CREATE_SEWAGESYSTEM,
    UPDATE_SEWAGESYSTEM,
    CREATE_VESSEL_SPECIFICS,
    UPDATE_VESSEL_SPECIFICS,
    CREATE_INVENTORY_CATEGORY,
    CREATE_VESSEL,
    DELETE_FUELTANK,
} from '@/app/lib/graphQL/mutation'
import {
    GET_ENGINES,
    GET_FUELTANKS,
    VESSEL_INFO,
    GET_SEWAGESYSTEMS,
    GET_WATERTANKS,
    GET_FILES,
} from '@/app/lib/graphQL/query'
import { SealogsVesselsIcon } from '../../lib/icons/SealogsVesselsIcon'
import DepartmentMultiSelectDropdown from '../department/multiselect-dropdown'
import FileUpload from '../../../components/file-upload'

import {
    getPermissions,
    hasPermission,
    isAdmin,
} from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { isEmpty, trim } from 'lodash'
import VesselModel from '@/app/offline/models/vessel'
import FuelTankModel from '@/app/offline/models/fuelTank'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { FooterWrapper } from '@/components/footer-wrapper'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { H4, P } from '@/components/ui/typography'
import { Textarea } from '@/components/ui/textarea'
import DatePicker from '@/components/DateRange'
import { AlertDialogNew, ListHeader } from '@/components/ui'
import { ArrowLeft, Plus } from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { formatDBDateTime } from '@/app/helpers/dateHelper'
import CrewDutyDropdown from '@/components/filter/components/crew-duty-dropdown'
import responsiveLabel, {
    getResponsiveLabel,
} from '../../../../utils/responsiveLabel'

export default function EditVessel({ vesselId }: { vesselId: number }) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [createVesselID, setCreateVesselID] = useState<number>(vesselId)
    const [vessel, setVessel] = useState({} as any)
    const [inventoryList, setInventoryList] = useState<any>()
    const [vesselInventories, setVesselInventory] = useState<any>([])
    const [inventoryCategories, setInventoryCategories] = useState<any>()
    const [engineList, setEngineList] = useState<any>()
    const [selectedEngine, setSelectedEngine] = useState<number>(0)
    const [fuelTankList, setFuelTankList] = useState<any>()
    const [selectedFuelTank, setSelectedFuelTank] = useState<number>(0)
    const [waterTankList, setWaterTankList] = useState<any>()
    const [selectedWaterTank, setSelectedWaterTank] = useState<number>(0)
    const [sewageSystemList, setSewageSystemList] = useState<any>()
    const [selectedSewageSystem, setSelectedSewageSystem] = useState<number>(0)
    const [membersList, setMembersList] = useState<any>()
    const [vesselMembers, setVesselMembers] = useState<any>()
    const [openEngineDialog, setOpenEngineDialog] = useState(false)
    const [openFuelTankDialog, setOpenFuelTankDialog] = useState(false)
    const [openCreateInventoryDialog, setOpenCreateInventoryDialog] =
        useState(false)
    const [openCreateMemberDialog, setOpenCreateMemberDialog] = useState(false)
    const [openWaterTankDialog, setOpenWaterTankDialog] = useState(false)
    const [openSewageSystemDialog, setOpenSewageSystemDialog] = useState(false)
    const [currentData, setCurrentData] = useState<any>({})
    const [displayOnDashboard, setDisplayOnDashboard] = useState<boolean>(false)
    const [displayLogbookComments, setDisplayLogbookComments] =
        useState<boolean>(false)
    const [selectedInventoryCategory, setSelectedInventoryCategory] =
        useState<any>()
    const [error, setError] = useState<any>(false)
    const [dateOfBuild, setDateOfBuild] = useState<any>()

    const [imageURI, setImageURI] = useState<any>()
    const [uploadImagedUrl, setUploadImagedUrl] = useState<any>('')
    const [uploadImagedID, setUploadImagedID] = useState<any>(null)
    const [Files, setFiles] = useState<any>([])
    const [imageLoader, setImageLoader] = useState(false)
    const [openVesselImageDialog, setOpenVesselImageDialog] = useState(false)
    const [vesselIcon, setVesselIcon] = useState<any>(false)
    const [vesselIconMode, setVesselIconMode] = useState('Icon')
    const [departmentList, setDepartmentList] = useState<any>(false)
    const [selectedCountry, setSelectedCountry] = useState<any>()
    const [selectedVesselType, setSelectedVesselType] = useState<any>()
    const [selectedEngineType, setSelectedEngineType] = useState<any>()
    const [vesselPhoto, setVesselPhoto] = useState<any>([])
    const [members, setMembers] = useState<any>([])
    const [openDeleteEngineDialog, setOpenDeleteEngineDialog] = useState(false)
    const vesselModel = new VesselModel()
    const [
        openCreateInventoryCategoryDialog,
        setOpenCreateInventoryCategoryDialog,
    ] = useState<boolean>(false)
    const [openArchiveVesselDialog, setOpenArchiveVesselDialog] =
        useState(false)
    const [selectedDepartments, setSelectedDepartments] = useState<any>([])
    const [userGroups, setUserGroups] = useState<any>()
    const [groups, setGroups] = useState<any>()
    const [crewDutyID, setCrewDutyID] = useState<number>(0)
    const fuelTankModel = new FuelTankModel()

    const handleSetInventoryCategories = (data: any) => {
        const categoriesList = [
            {
                label: ' ---- Create Category ---- ',
                value: 'newCategory',
            },
            ...data
                ?.filter((category: any) => category.name !== null)
                .map((category: any) => ({
                    label: category.name,
                    value: category.id,
                })),
        ]
        setInventoryCategories(categoriesList)
    }

    getInventoryCategory(handleSetInventoryCategories)

    getDepartmentList(setDepartmentList)

    const [queryCreateVessel, { data: createVesselData }] = useMutation(
        CREATE_VESSEL,
        {
            fetchPolicy: 'no-cache',
            onCompleted: (response: any) => {
                const data = response.createVessel
                if (data.id > 0) {
                    queryUpdateVesselWithoutReload({
                        variables: {
                            input: {
                                id: data.id,
                                icon: vesselIcon
                                    ? vesselIcon.replaceAll(' ', '-')
                                    : null,
                                iconMode: vesselIconMode,
                                photoID:
                                    vesselPhoto.length > 0
                                        ? vesselPhoto[0].id
                                        : null,
                            },
                        },
                    })
                }
            },
            onError: (error: any) => {
                console.error('createVessel error', error)
            },
        },
    )

    const createNewVessel = async () => {
        let newVesselID = createVesselID
        if (createVesselID === 0) {
            const Title =
                (document.getElementById('vessel-title') as HTMLInputElement)
                    .value || 'New Vessel'
            const AuthNo = (
                document.getElementById('vessel-authNo') as HTMLInputElement
            ).value
            const MMSI = (
                document.getElementById('vessel-mmsi') as HTMLInputElement
            ).value
            const TransitId = (
                document.getElementById('vessel-transitId') as HTMLInputElement
            ).value
            const Country = currentData.countryofoperation
                ? currentData.countryofoperation.value
                : vessel?.countryOfOperation
            let vesselType = currentData.vesseltype
                ? currentData.vesseltype.value || defaultVesselType.value
                : vessel?.vesselType || defaultVesselType.value
            vesselType = vesselType.replaceAll('_', ' ') // replace underscores with spaces for vessel type before saving
            const vesselDescription = (
                document.getElementById(
                    'vessel-description',
                ) as HTMLInputElement
            ).value
            const minCrew = (
                document.getElementById('vessel-minCrew') as HTMLInputElement
            ).value
            const maxPax = (
                document.getElementById('vessel-maxPax') as HTMLInputElement
            ).value
            const maxPOB = (
                document.getElementById('vessel-maxPOB') as HTMLInputElement
            ).value
            const callSign = (
                document.getElementById('vessel-callSign') as HTMLInputElement
            ).value
            const { data } = await queryCreateVessel({
                variables: {
                    input: {
                        mmsi: MMSI,
                        registration: AuthNo,
                        title: Title,
                        countryOfOperation: Country,
                        transitID: TransitId,
                        showOnDashboard: displayOnDashboard,
                        displayLogbookComments: displayLogbookComments,
                        vesselType: vesselType,
                        vesselTypeDescription: vesselDescription,
                        callSign: callSign,
                        minCrew: +minCrew,
                        maxPax: +maxPax,
                        maxPOB: +maxPOB,
                        vesselSpecificsID: 0,
                        seaLogsMembers: vesselMembers
                            ?.map((member: any) => member.value)
                            .join(','),
                    },
                },
            })
            newVesselID = +data.createVessel.id
            setCreateVesselID(newVesselID)
        }
        return newVesselID
    }

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const getEngines = async (engineIds: any) => {
        await queryGetEngines({
            variables: {
                id: engineIds,
            },
        })
    }

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            let data = response.readFuelTanks.nodes
            if (data) {
                // Note from Esthon: I added this map function to replace the underscores in Unleaded_91 and Unleaded_95 with a space
                // This will fix this issue: Expected a value of type "FuelTypeEnum" but received: (empty string)
                data = data.map((item: any) => ({
                    ...item,
                    fuelType:
                        trim(item.fuelType?.replaceAll('_', ' ')) === ''
                            ? null
                            : item.fuelType?.replaceAll('_', ' '),
                }))
                setFuelTankList(data)
            }
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        await queryGetFuelTanks({
            variables: {
                id: fuelTankIds,
            },
        })
    }

    const [queryGetWaterTanks] = useLazyQuery(GET_WATERTANKS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readWaterTanks.nodes
            setWaterTankList(data)
        },
        onError: (error: any) => {
            console.error('getWaterTanks error', error)
        },
    })

    const getWaterTanks = async (waterTankIds: any) => {
        await queryGetWaterTanks({
            variables: {
                id: waterTankIds,
            },
        })
    }

    const [queryGetSewageSystems] = useLazyQuery(GET_SEWAGESYSTEMS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readSewageSystems.nodes
            setSewageSystemList(data)
        },
        onError: (error: any) => {
            console.error('getSewageSystems error', error)
        },
    })

    const getSewageSystems = async (sewageSystemIds: any) => {
        await queryGetSewageSystems({
            variables: {
                id: sewageSystemIds,
            },
        })
    }

    const handleSetVessel = (data: any) => {
        // Preserve current form state for new vessels to prevent clearing user input
        const isNewVessel = vesselId === 0
        const currentDateOfBuild = dateOfBuild
        const currentVesselState = vessel
        const currentSelectedVesselType = selectedVesselType
        const engineIds = data?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'Engine',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const fuelTankIds = data?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const waterTankIds = data?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'WaterTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const sewageSystemIds = data?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'SewageSystem',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        engineIds.length > 0 && getEngines(engineIds)
        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
        waterTankIds.length > 0 && getWaterTanks(waterTankIds)
        sewageSystemIds.length > 0 && getSewageSystems(sewageSystemIds)
        setVesselMembers(
            data.seaLogsMembers.nodes.map((member: any) => {
                return {
                    label: member?.firstName + ' ' + member.surname,
                    value: member.id,
                }
            }),
        )
        setSelectedCountry(
            CountriesList.find(
                (country: any) => country.value === data.countryOfOperation,
            ),
        )

        // For new vessels, preserve user's vessel type selection
        if (isNewVessel && currentSelectedVesselType) {
            // Keep user's selection for new vessels
            setSelectedVesselType(currentSelectedVesselType)
            setCurrentData({
                countryofoperation: data.countryOfOperation,
                vesseltype: currentSelectedVesselType,
                engine: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )?.basicComponent,
                fuelTank: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'FuelTank',
                )?.basicComponent,
                waterTank: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'WaterTank',
                )?.basicComponent,
                sewageSystem: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory ===
                        'SewageSystem',
                )?.basicComponent,
            })
        } else {
            // Use server data for existing vessels
            setSelectedVesselType(
                vesselTypes.find(
                    (vesselType: any) => vesselType.value === data.vesselType,
                ),
            )
            setCurrentData({
                countryofoperation: data.countryOfOperation,
                vesseltype: data.vesselType || defaultVesselType.value,
                engine: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'Engine',
                )?.basicComponent,
                fuelTank: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'FuelTank',
                )?.basicComponent,
                waterTank: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory === 'WaterTank',
                )?.basicComponent,
                sewageSystem: data?.parentComponent_Components?.nodes.find(
                    (item: any) =>
                        item.basicComponent.componentCategory ===
                        'SewageSystem',
                )?.basicComponent,
            })
        }

        // For new vessels, preserve user input for dateOfBuild and dangerous goods
        if (
            isNewVessel &&
            !data?.vesselSpecifics?.dateOfBuild &&
            currentDateOfBuild
        ) {
            // Keep the user's date input if server doesn't have vessel specifics yet
            setDateOfBuild(currentDateOfBuild)
        } else {
            // Use server data for existing vessels or when server has the data
            setDateOfBuild(
                data?.vesselSpecifics?.dateOfBuild
                    ? new Date(data?.vesselSpecifics?.dateOfBuild)
                    : null,
            )
        }

        // For new vessels, preserve dangerous goods checkbox state
        if (isNewVessel && currentVesselState?.vesselSpecifics) {
            // Preserve the current vessel state including dangerous goods checkbox
            setVessel({
                ...data,
                vesselSpecifics: {
                    ...data.vesselSpecifics,
                    carriesDangerousGoods:
                        currentVesselState.vesselSpecifics
                            .carriesDangerousGoods,
                },
            })
        } else {
            // For existing vessels or when no current state, use server data
            setVessel(data)
        }

        setDisplayOnDashboard(data.showOnDashboard)
        setDisplayLogbookComments(data.displayLogbookComments)
        setSelectedDepartments(data.departments?.nodes.map((d: any) => d.id))
        if (data.bannerImageID) {
            getFileDetails({
                variables: {
                    id: [data.bannerImageID],
                },
            })
        }
        data?.icon && setVesselIcon(data.icon.replaceAll('-', ' '))
        data?.iconMode && setVesselIconMode(data.iconMode)
        data?.photoID > 0 && loadVesselPhoto(data.photoID)
        data?.bannerImageID > 0 && setUploadImagedID(data.bannerImageID)
    }

    const loadVesselPhoto = async (id: string) => {
        await queryFiles({
            variables: {
                id: [id],
            },
        })
    }

    const [queryFiles] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readFiles.nodes[0]
            setVesselPhoto([data])
        },
        onError: (error: any) => {
            console.error('queryFilesEntry error', error)
        },
    })

    const [queryVesselInfo] = useLazyQuery(VESSEL_INFO, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneVessel
            if (data) {
                handleSetVessel(data)
            }
        },
        onError: (error: any) => {
            console.error('queryVesselInfo error', error)
        },
    })

    const loadVesselInfo = async () => {
        await queryVesselInfo({
            variables: {
                id: +createVesselID,
            },
        })
    }

    const [queryCreateParentComponent] = useMutation(CREATE_PARENT_COMPONENT, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createParentComponent_Component
            if (data.id > 0) {
                loadVesselInfo()
            }
        },
        onError: (error: any) => {
            console.error('createParentComponent error', error)
        },
    })

    const createParentComponent = async (engineId: number) => {
        const vID = await createNewVessel()
        await queryCreateParentComponent({
            variables: {
                input: {
                    parentComponentID: vID,
                    basicComponentID: engineId,
                },
            },
        })
    }
    const handlesSetInventoryList = (data: any) => {
        // Show only inventory not assigned to any vessel.
        const filteredData = data.filter((item: any) => item.vesselID <= 0)
        const appendData = [
            { label: '--- Create Inventory ---', value: 'newInventory' },
            ...filteredData
                .filter(
                    (item: any) => item.archived == false && item.title != null,
                )
                .map((item: any) => {
                    return { label: item.title, value: item.id }
                }),
        ]
        setInventoryList(appendData)
    }

    getInventoryList(handlesSetInventoryList)

    const doSetMemberList = (data: any) => {
        const deptList =
            vessel.departments?.nodes.flatMap(
                (department: any) => department.id,
            ) ?? []
        const appendData = [
            { label: '--- Create Crew Member ---', value: 'newCrewMember' },
            ...data
                .filter(
                    (item: any) =>
                        item.archived == false &&
                        item.firstName !== null &&
                        (item.departments.nodes.length == 0 ||
                            item.departments.nodes.some((d: any) =>
                                deptList.includes(d.id),
                            )),
                )
                .map((item: any) => {
                    return {
                        label: item.firstName + ' ' + item?.surname,
                        value: item.id,
                    }
                }),
        ]
        setMembersList(appendData)
    }
    const handlesSetMembersList = (data: any) => {
        setMembers(data)
        doSetMemberList(data)
    }
    getSeaLogsMembersList(handlesSetMembersList)

    const handleSetGroups = (groups: any) => {
        let groupsResponse = groups
            .filter((group: any) => isAdmin() || group.code !== 'admin')
            .map((group: any) => {
                return {
                    label: group.title,
                    value: group.id,
                }
            })
        setGroups(groupsResponse)
    }

    getSeaLogsGroups(handleSetGroups)

    // Uncomment the filter to only show non-archived inventory items.
    const handleSetVesselInventory = (data: any) => {
        if (vesselId > 0) {
            const appendData = data
                // .filter((item: any) => item.archived == false)
                .map((item: any) => {
                    return { label: item.title, value: item.id }
                })
            setVesselInventory(appendData)
        }
    }

    getInventoryByVesselId(createVesselID, handleSetVesselInventory)

    const [queryUpdateEngine] = useMutation(UPDATE_ENGINE, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateEngine
            if (data.id > 0) {
                setSelectedEngine(0)
                setOpenEngineDialog(false)
                const engineIds = vessel?.parentComponent_Components?.nodes
                    ? vessel?.parentComponent_Components?.nodes
                          .filter(
                              (item: any) =>
                                  item.basicComponent.componentCategory ===
                                  'Engine',
                          )
                          .map((item: any) => {
                              return item.basicComponent.id
                          })
                    : []
                engineIds.length > 0 && getEngines(engineIds)
            }
        },
        onError: (error: any) => {
            console.error('updateEngine error', error)
        },
    })
    const [queryAddEngine] = useMutation(CREATE_ENGINE, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createEngine
            if (data.id > 0) {
                setOpenEngineDialog(false)
                const engineIds = [
                    data.id,
                    ...(vessel?.parentComponent_Components?.nodes
                        ? vessel.parentComponent_Components.nodes
                              .filter(
                                  (item: any) =>
                                      item.basicComponent.componentCategory ===
                                      'Engine',
                              )
                              .map((item: any) => {
                                  return item.basicComponent.id
                              })
                        : []),
                ]
                engineIds.length > 0 && getEngines(engineIds)
                createParentComponent(data.id)
            }
        },
        onError: (error: any) => {
            console.error('createEngine error', error)
        },
    })
    const handleAddNewEngine = async () => {
        const engineName = (
            document.getElementById('engine-title') as HTMLInputElement
        ).value
        const engineType = currentData.engine
            ? currentData.engine.type?.replaceAll('_', ' ')
            : ''
        const enginePower = (
            document.getElementById('engine-power') as HTMLInputElement
        ).value
        const engineDrive = currentData.engine
            ? currentData.engine.driveType?.replaceAll('_', ' ')
            : ''
        const enginePosition = currentData.engine
            ? currentData.engine.positionOnVessel
            : ''
        const engineHours = (
            document.getElementById('engine-hours') as HTMLInputElement
        ).value
        const engineIdentified = (
            document.getElementById('engine-identified') as HTMLInputElement
        ).value
        const engineMake = (
            document.getElementById('engine-make') as HTMLInputElement
        ).value
        const engineModel = (
            document.getElementById('engine-model') as HTMLInputElement
        ).value
        const engineKW = (
            document.getElementById('engine-kW') as HTMLInputElement
        ).value
        const engineKVA = (
            document.getElementById('engine-kVA') as HTMLInputElement
        ).value
        vesselModel.delete(vesselId)
        if (selectedEngine > 0) {
            await queryUpdateEngine({
                variables: {
                    input: {
                        id: selectedEngine,
                        title: engineName,
                        type: engineType,
                        maxPower: enginePower,
                        driveType: engineDrive,
                        positionOnVessel: enginePosition,
                        currentHours: +engineHours,
                        identifier: engineIdentified,
                        componentCategory: 'Engine',
                        make: engineMake,
                        model: engineModel,
                        kW: engineKW,
                        kVA: engineKVA,
                    },
                },
            })
        } else {
            await queryAddEngine({
                variables: {
                    input: {
                        title: engineName,
                        type: engineType,
                        maxPower: enginePower,
                        driveType: engineDrive,
                        positionOnVessel: enginePosition,
                        currentHours: +engineHours,
                        identifier: engineIdentified,
                        componentCategory: 'Engine',
                        make: engineMake,
                        model: engineModel,
                        kW: engineKW,
                        kVA: engineKVA,
                    },
                },
            })
        }
    }

    const handleDeleteEngine = async () => {
        selectedEngine > 0 &&
            (await queryDeleteEngine({
                variables: {
                    input: {
                        id: selectedEngine,
                        archived: true,
                    },
                },
            }))
    }

    const [queryDeleteEngine] = useMutation(UPDATE_ENGINE, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateEngine
            if (data.id > 0) {
                setEngineList(
                    engineList.filter((item: any) => item.id != data.id),
                )
                setOpenDeleteEngineDialog(false)
                setSelectedEngine(0)
                setOpenEngineDialog(false)
            }
        },
        onError: (error: any) => {
            console.error('updateEngine error', error)
        },
    })

    const handleAddNewFuelTank = async () => {
        const fuelTankName = (
            document.getElementById('fuel-tank-title') as HTMLInputElement
        ).value
        const fuelTankSafeCapacity = (
            document.getElementById(
                'fuel-tank-safeCapacity',
            ) as HTMLInputElement
        ).value
        const fuelTankCapacity = (
            document.getElementById('fuel-tank-capacity') as HTMLInputElement
        ).value
        const fuelTankIdentified = (
            document.getElementById('fuel-tank-identified') as HTMLInputElement
        ).value

        const data = {
            title: fuelTankName,
            safeFuelCapacity: +fuelTankSafeCapacity,
            capacity: +fuelTankCapacity,
            identifier: fuelTankIdentified,
            fuelType:
                trim(currentData.fuelTank?.fuelType) === ''
                    ? null
                    : currentData.fuelTank?.fuelType,
            dipType: currentData.fuelTank ? currentData.fuelTank.dipType : null,
            componentCategory: 'FuelTank',
            currentLevel: +currentData.fuelTank.currentLevel,
        }
        if (selectedFuelTank > 0) {
            fuelTankModel.update({
                id: selectedFuelTank,
                ...data,
            })
            vesselModel.delete(vesselId)
            await queryUpdateFuelTank({
                variables: {
                    input: {
                        id: selectedFuelTank,
                        ...data,
                    },
                },
            })
        } else {
            await queryAddFuelTank({
                variables: {
                    input: {
                        ...data,
                    },
                },
            })
        }
    }

    const [queryDeleteFuelTank] = useMutation(DELETE_FUELTANK, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.deleteFuelTanks
            setFuelTankList(
                fuelTankList.filter((item: any) => !data.includes(item.id)),
            )
            setSelectedFuelTank(0)
            setOpenFuelTankDialog(false)
        },
        onError: (error: any) => {
            console.error('deleteFuelTank error', error)
        },
    })

    const handleDeleteFuelTank = async (id: number) => {
        await queryDeleteFuelTank({
            variables: {
                ids: [id],
            },
        })
    }

    const [queryAddWaterTank] = useMutation(CREATE_WATERTANK, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createWaterTank
            if (data.id > 0) {
                setOpenWaterTankDialog(false)
                const waterTankIds = [
                    data.id,
                    ...(vessel?.parentComponent_Components?.nodes
                        ? vessel?.parentComponent_Components?.nodes
                              ?.filter(
                                  (item: any) =>
                                      item.basicComponent.componentCategory ===
                                      'WaterTank',
                              )
                              .map((item: any) => {
                                  return item.basicComponent.id
                              })
                        : []),
                ]
                waterTankIds.length > 0 && getWaterTanks(waterTankIds)
                createParentComponent(data.id)
            }
        },
        onError: (error: any) => {
            console.error('createWaterTank error', error)
        },
    })

    const handleAddNewWaterTank = async () => {
        const waterTankName = (
            document.getElementById('water-tank-title') as HTMLInputElement
        ).value
        const waterTankCapacity = (
            document.getElementById('water-tank-capacity') as HTMLInputElement
        ).value
        const waterTankIdentified = (
            document.getElementById('water-tank-identified') as HTMLInputElement
        ).value
        if (selectedWaterTank > 0) {
            await queryUpdateWaterTank({
                variables: {
                    input: {
                        id: selectedWaterTank,
                        title: waterTankName,
                        capacity: +waterTankCapacity,
                        identifier: waterTankIdentified,
                        componentCategory: 'WaterTank',
                    },
                },
            })
        } else {
            await queryAddWaterTank({
                variables: {
                    input: {
                        title: waterTankName,
                        capacity: +waterTankCapacity,
                        identifier: waterTankIdentified,
                        componentCategory: 'WaterTank',
                    },
                },
            })
        }
    }

    const handleAddNewSewageSystem = async () => {
        const sewageSystemName = (
            document.getElementById('sewage-system-title') as HTMLInputElement
        ).value
        const sewageSystemCapacity = (
            document.getElementById(
                'sewage-system-capacity',
            ) as HTMLInputElement
        ).value
        const sewageSystemIdentified = (
            document.getElementById(
                'sewage-system-identified',
            ) as HTMLInputElement
        ).value
        const numberOfTanks = (
            document.getElementById(
                'sewage-system-numberOfTanks',
            ) as HTMLInputElement
        ).value
        if (selectedSewageSystem > 0) {
            await queryUpdateSewageSystem({
                variables: {
                    input: {
                        id: selectedSewageSystem,
                        title: sewageSystemName,
                        capacity: +sewageSystemCapacity,
                        identifier: sewageSystemIdentified,
                        numberOfTanks: +numberOfTanks,
                        componentCategory: 'SewageSystem',
                    },
                },
            })
        } else {
            await queryAddSewageSystem({
                variables: {
                    input: {
                        title: sewageSystemName,
                        capacity: +sewageSystemCapacity,
                        identifier: sewageSystemIdentified,
                        numberOfTanks: +numberOfTanks,
                        componentCategory: 'SewageSystem',
                    },
                },
            })
        }
    }

    const [queryUpdateWaterTank] = useMutation(UPDATE_WATERTANK, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateWaterTank
            if (data.id > 0) {
                setSelectedWaterTank(0)
                setOpenWaterTankDialog(false)
                const waterTankIds = vessel?.parentComponent_Components?.nodes
                    ? vessel?.parentComponent_Components?.nodes
                          .filter(
                              (item: any) =>
                                  item.basicComponent.componentCategory ===
                                  'WaterTank',
                          )
                          .map((item: any) => {
                              return item.basicComponent.id
                          })
                    : []
                waterTankIds.length > 0 && getWaterTanks(waterTankIds)
            }
        },
        onError: (error: any) => {
            console.error('updateWaterTank error', error)
        },
    })

    const [queryAddSewageSystem] = useMutation(CREATE_SEWAGESYSTEM, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createSewageSystem
            if (data.id > 0) {
                setOpenSewageSystemDialog(false)
                const sewageSystemIds = [
                    data.id,
                    ...(vessel?.parentComponent_Components?.nodes
                        ? vessel?.parentComponent_Components?.nodes
                              .filter(
                                  (item: any) =>
                                      item.basicComponent.componentCategory ===
                                      'SewageSystem',
                              )
                              .map((item: any) => {
                                  return item.basicComponent.id
                              })
                        : []),
                ]
                sewageSystemIds.length > 0 && getSewageSystems(sewageSystemIds)
                createParentComponent(data.id)
            }
        },
        onError: (error: any) => {
            console.error('createSewageSystem error', error)
        },
    })

    const [queryUpdateSewageSystem] = useMutation(UPDATE_SEWAGESYSTEM, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateSewageSystem
            if (data.id > 0) {
                setSelectedSewageSystem(0)
                setOpenSewageSystemDialog(false)
                const sewageSystemIds = vessel?.parentComponent_Components
                    ?.nodes
                    ? vessel?.parentComponent_Components?.nodes
                          .filter(
                              (item: any) =>
                                  item.basicComponent.componentCategory ===
                                  'SewageSystem',
                          )
                          .map((item: any) => {
                              return item.basicComponent.id
                          })
                    : []
                sewageSystemIds.length > 0 && getSewageSystems(sewageSystemIds)
            }
        },
        onError: (error: any) => {
            console.error('updateSewageSystem error', error)
        },
    })

    const [queryAddFuelTank] = useMutation(CREATE_FUELTANK, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createFuelTank
            const fuelTankName = (
                document.getElementById('fuel-tank-title') as HTMLInputElement
            ).value
            const fuelTankSafeCapacity = (
                document.getElementById(
                    'fuel-tank-safeCapacity',
                ) as HTMLInputElement
            ).value
            const fuelTankCapacity = (
                document.getElementById(
                    'fuel-tank-capacity',
                ) as HTMLInputElement
            ).value
            const fuelTankIdentified = (
                document.getElementById(
                    'fuel-tank-identified',
                ) as HTMLInputElement
            ).value

            const fuelTank = {
                id: data.id,
                title: fuelTankName,
                safeFuelCapacity: +fuelTankSafeCapacity,
                capacity: +fuelTankCapacity,
                identifier: fuelTankIdentified,
                fuelType:
                    trim(currentData.fuelTank?.fuelType) === ''
                        ? null
                        : currentData.fuelTank?.fuelType,
                dipType: currentData.fuelTank
                    ? currentData.fuelTank.dipType
                    : null,
                componentCategory: 'FuelTank',
                currentLevel: +currentData.fuelTank.currentLevel,
            }
            fuelTankModel.save(fuelTank)
            vesselModel.delete(vesselId)
            if (data.id > 0) {
                setOpenFuelTankDialog(false)
                const fuelTankIds = [
                    data.id,
                    ...(vessel?.parentComponent_Components?.nodes
                        ? vessel?.parentComponent_Components?.nodes
                              .filter(
                                  (item: any) =>
                                      item.basicComponent.componentCategory ===
                                      'FuelTank',
                              )
                              .map((item: any) => {
                                  return item.basicComponent.id
                              })
                        : []),
                ]
                fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
                createParentComponent(data.id)
            }
        },
        onError: (error: any) => {
            console.error('createFuelTank error', error)
        },
    })

    const [queryUpdateFuelTank] = useMutation(UPDATE_FUELTANK, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateFuelTank
            if (data.id > 0) {
                setSelectedFuelTank(0)
                setOpenFuelTankDialog(false)
                const fuelTankIds = vessel?.parentComponent_Components?.nodes
                    ? vessel?.parentComponent_Components?.nodes
                          .filter(
                              (item: any) =>
                                  item.basicComponent.componentCategory ===
                                  'FuelTank',
                          )
                          .map((item: any) => {
                              return item.basicComponent.id
                          })
                    : []
                fuelTankIds.length > 0 && getFuelTanks(fuelTankIds)
            }
        },
        onError: (error: any) => {
            console.error('updateFuelTank error', error)
        },
    })

    const handleUpdate = async (vesselSpecificsID = 0) => {
        const Title = (
            document.getElementById('vessel-title') as HTMLInputElement
        ).value
        const AuthNo = (
            document.getElementById('vessel-authNo') as HTMLInputElement
        ).value
        const MMSI = (
            document.getElementById('vessel-mmsi') as HTMLInputElement
        ).value
        const TransitId = (
            document.getElementById('vessel-transitId') as HTMLInputElement
        ).value
        const Country = currentData.countryofoperation
            ? currentData.countryofoperation.value
            : vessel?.countryOfOperation
        let vesselType = currentData.vesseltype
            ? currentData.vesseltype.value || defaultVesselType.value
            : vessel?.vesselType || defaultVesselType.value
        vesselType = vesselType.replaceAll('_', ' ') // replace underscores with spaces for vessel type before saving
        const vesselDescription = (
            document.getElementById('vessel-description') as HTMLInputElement
        ).value
        const minCrew = (
            document.getElementById('vessel-minCrew') as HTMLInputElement
        ).value
        const maxPax = (
            document.getElementById('vessel-maxPax') as HTMLInputElement
        ).value
        const maxPOB = (
            document.getElementById('vessel-maxPOB') as HTMLInputElement
        ).value
        const callSign = (
            document.getElementById('vessel-callSign') as HTMLInputElement
        ).value
        // Vessel Specifics
        const beam = (
            document.getElementById('vessel-beam') as HTMLInputElement
        ).value
        const overallLength = (
            document.getElementById('vessel-overallLength') as HTMLInputElement
        ).value
        const draft = (
            document.getElementById('vessel-draft') as HTMLInputElement
        ).value
        const hullConstruction = (
            document.getElementById(
                'vessel-hullConstruction',
            ) as HTMLInputElement
        ).value
        const hullColor = (
            document.getElementById('vessel-hullColor') as HTMLInputElement
        ).value
        const primaryHarbour = (
            document.getElementById('vessel-primaryHarbour') as HTMLInputElement
        ).value

        const vID = await createNewVessel()
        if (vesselSpecificsID > 0) {
            await queryUpdateVessel({
                variables: {
                    input: {
                        id: vID,
                        mmsi: MMSI,
                        registration: AuthNo,
                        title: Title,
                        countryOfOperation: Country,
                        transitID: TransitId,
                        showOnDashboard: displayOnDashboard,
                        displayLogbookComments: displayLogbookComments,
                        vesselType: vesselType,
                        vesselTypeDescription: vesselDescription,
                        callSign: callSign,
                        minCrew: +minCrew,
                        maxPax: +maxPax,
                        maxPOB: +maxPOB,
                        vesselSpecificsID: vesselSpecificsID,
                        seaLogsMembers: vesselMembers
                            ?.map((member: any) => member.value)
                            .join(','),
                        departments: selectedDepartments.join(','),
                        bannerImageID: uploadImagedID,
                    },
                },
            })
        }
        if (vesselSpecificsID == 0) {
            if (vessel?.vesselSpecifics?.id > 0) {
                await queryUpdateVesselSpecifics({
                    variables: {
                        input: {
                            id: vessel?.vesselSpecifics.id,
                            beam: beam,
                            overallLength: overallLength,
                            dateOfBuild: formatDBDateTime(dateOfBuild),
                            draft: draft,
                            carriesDangerousGoods:
                                vessel?.vesselSpecifics
                                    ?.carriesDangerousGoods || false,
                            carriesVehicles:
                                vessel?.vesselSpecifics?.carriesVehicles ||
                                false,
                            hullConstruction: hullConstruction,
                            hullColor: hullColor,
                            primaryHarbour: primaryHarbour,
                        },
                    },
                })
            } else {
                await queryCreateVesselSpecifics({
                    variables: {
                        input: {
                            beam: beam,
                            overallLength: overallLength,
                            dateOfBuild: formatDBDateTime(dateOfBuild),
                            draft: draft,
                            hullConstruction: hullConstruction,
                            hullColor: hullColor,
                            carriesDangerousGoods:
                                vessel?.vesselSpecifics
                                    ?.carriesDangerousGoods || false,
                            carriesVehicles:
                                vessel?.vesselSpecifics?.carriesVehicles ||
                                false,
                            primaryHarbour: primaryHarbour,
                            vesselID: vID,
                        },
                    },
                })
            }
        }
    }

    const [queryCreateVesselSpecifics] = useMutation(CREATE_VESSEL_SPECIFICS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createVesselSpecifics
            if (data.id > 0) {
                handleUpdate(data.id)
            }
        },
        onError: (error: any) => {
            console.error('createVesselSpecifics error', error)
        },
    })

    const [queryUpdateVesselSpecifics] = useMutation(UPDATE_VESSEL_SPECIFICS, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateVesselSpecifics
            if (data.id > 0) {
                vesselModel.delete(data.id)
                handleUpdate(data.id)
            }
        },
        onError: (error: any) => {
            console.error('updateVesselSpecifics error', error)
        },
    })

    const [queryUpdateVessel] = useMutation(UPDATE_VESSEL, {
        onCompleted: (response: any) => {
            const data = response.updateVessel
            if (data.id > 0) {
                vesselModel.delete(data.id)
                router.push(
                    `/vessel/info?id=${vesselId > 0 ? vesselId : data.id}`,
                )
            }
        },
        onError: (error: any) => {
            console.error('updateVessel error', error)
        },
    })

    const [queryUpdateVesselWithoutReload] = useMutation(UPDATE_VESSEL, {
        onCompleted: (response: any) => {
            const data = response.updateVessel
            if (data.id > 0) {
                vesselModel.delete(data.id)
            }
        },
        onError: (error: any) => {
            console.error('updateVessel error', error)
        },
    })

    const handleDelete = async () => {
        if (isAdmin()) {
            const vID = await createNewVessel()
            await queryArchiveVessel({
                variables: {
                    input: {
                        id: vID,
                        archived: true,
                    },
                },
            })
        } else {
            alert('You do not have permission to archive vessel')
        }
    }

    const [queryArchiveVessel] = useMutation(UPDATE_VESSEL, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateVessel
            if (data.id > 0) {
                vesselModel.delete(data.id)
                router.push('/vessel')
            }
        },
        onError: (error: any) => {
            console.error('archiveVessel error', error)
        },
    })

    const handleSetSelectedEngine = (engineId: number) => {
        setSelectedEngine(engineId)
        const appendData = {
            ...currentData,
            engine: engineList.find((engine: any) => engine.id == engineId),
        }
        setCurrentData(appendData)
    }

    const handleSetSelectedFuelTank = (fuelTankId: number) => {
        setSelectedFuelTank(fuelTankId)
        const appendData = {
            ...currentData,
            fuelTank: fuelTankList.find(
                (fuelTank: any) => fuelTank.id == fuelTankId,
            ),
        }
        setCurrentData(appendData)
    }

    const handleSetSelectedWaterTank = (waterTankId: number) => {
        setSelectedWaterTank(waterTankId)
        const appendData = {
            ...currentData,
            waterTank: waterTankList.find(
                (waterTank: any) => waterTank.id == waterTankId,
            ),
        }
        setCurrentData(appendData)
    }

    const handleSetSelectedSewageSystem = (sewageSystemId: number) => {
        setSelectedSewageSystem(sewageSystemId)
        const appendData = {
            ...currentData,
            sewageSystem: sewageSystemList.find(
                (sewageSystem: any) => sewageSystem.id == sewageSystemId,
            ),
        }
        setCurrentData(appendData)
    }

    const handleInventoryChange = (value: any) => {
        if (value.find((option: any) => option.value === 'newInventory')) {
            setOpenCreateInventoryDialog(true)
        } else {
            setVesselInventory(value)
            const deletedValues = vesselInventories?.filter((inventory: any) =>
                value.every((option: any) => option.value !== inventory.value),
            )
            deletedValues &&
                deletedValues.length > 0 &&
                deletedValues.map((inventory: any) => {
                    unsetVesselInventory(inventory.value)
                })
            const addedValues = value.filter((inventory: any) =>
                vesselInventories?.every(
                    (option: any) => option.value !== inventory.value,
                ),
            )
            addedValues &&
                addedValues.length > 0 &&
                addedValues.map((inventory: any) => {
                    setInventoryToVessel(inventory.value)
                })
        }
    }

    const setInventoryToVessel = async (inventoryId: number) => {
        const vID = await createNewVessel()
        await queryUpdateInventory({
            variables: {
                input: {
                    id: inventoryId,
                    vesselID: vID,
                },
            },
        })
    }

    const unsetVesselInventory = async (inventoryId: number) => {
        await queryUpdateInventory({
            variables: {
                input: {
                    id: inventoryId,
                    vesselID: null,
                },
            },
        })
    }

    const [queryUpdateInventory] = useMutation(UPDATE_INVENTORY, {
        fetchPolicy: 'no-cache',

        onError: (error: any) => {
            console.error('updateInventory error', error)
        },
    })

    const handleAddNewInventory = async () => {
        const vID = await createNewVessel()
        const variables = {
            input: {
                item: (
                    document.getElementById(
                        'inventory-item',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-item',
                          ) as HTMLInputElement
                      ).value
                    : null,
                description: (
                    document.getElementById(
                        'inventory-short-description',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-short-description',
                          ) as HTMLInputElement
                      ).value
                    : null,
                quantity: (
                    document.getElementById('inventory-qty') as HTMLInputElement
                ).value
                    ? parseInt(
                          (
                              document.getElementById(
                                  'inventory-qty',
                              ) as HTMLInputElement
                          ).value,
                      )
                    : null,
                productCode: (
                    document.getElementById(
                        'inventory-code',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-code',
                          ) as HTMLInputElement
                      ).value
                    : null,
                costingDetails: (
                    document.getElementById(
                        'inventory-cost',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-cost',
                          ) as HTMLInputElement
                      ).value
                    : null,
                location: (
                    document.getElementById(
                        'inventory-location',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-location',
                          ) as HTMLInputElement
                      ).value
                    : null,
                categories: currentData?.inventory?.category
                    ? currentData.inventory.category
                    : null,
                vesselID: vID,
            },
        }
        await queryAddInventory({
            variables: variables,
        })
    }

    const [queryAddInventory] = useMutation(CREATE_INVENTORY, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createInventory
            if (data.id > 0) {
                setOpenCreateInventoryDialog(false)
                setInventoryList([
                    ...inventoryList,
                    { label: data.item, value: data.id },
                ])
                setVesselInventory([
                    ...vesselInventories,
                    { label: data.item, value: data.id },
                ])
            }
        },
        onError: (error: any) => {
            console.error('createInventory error', error)
        },
    })

    const handleMemberChange = (value: any) => {
        if (value.find((option: any) => option.value === 'newCrewMember')) {
            setOpenCreateMemberDialog(true)
        } else {
            setVesselMembers(value)
        }
    }

    const handleDutyChange = (duty: any) => {
        setCrewDutyID(+duty.value)
    }

    const handleGroupChange = (group: any) => {
        setUserGroups(group)
    }

    const resetCrewMemberForm = () => {
        setUserGroups(null)
        setCrewDutyID(0)
        // Clear form inputs
        const inputs = [
            'crew-firstName',
            'crew-surname',
            'crew-username',
            'crew-password',
            'crew-email',
            'crew-phoneNumber',
        ]
        inputs.forEach((id) => {
            const element = document.getElementById(id) as HTMLInputElement
            if (element) element.value = ''
        })
    }

    const handleAddNewMember = async () => {
        const vID = await createNewVessel()
        const variables = {
            input: {
                firstName: (
                    document.getElementById(
                        'crew-firstName',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-firstName',
                          ) as HTMLInputElement
                      ).value
                    : null,
                surname: (
                    document.getElementById('crew-surname') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-surname',
                          ) as HTMLInputElement
                      ).value
                    : null,
                email: (
                    document.getElementById('crew-email') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-email',
                          ) as HTMLInputElement
                      ).value
                    : null,
                phoneNumber: (
                    document.getElementById(
                        'crew-phoneNumber',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-phoneNumber',
                          ) as HTMLInputElement
                      ).value
                    : null,
                username: (
                    document.getElementById('crew-username') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-username',
                          ) as HTMLInputElement
                      ).value
                    : null,
                password: (
                    document.getElementById('crew-password') as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'crew-password',
                          ) as HTMLInputElement
                      ).value
                    : null,
                primaryDutyID: crewDutyID > 0 ? crewDutyID : null,
                groups: userGroups ? [userGroups.value].join(',') : null,
                vehicles: [vID].join(','),
            },
        }
        await queryAddMember({
            variables: variables,
        })
    }

    const [queryAddMember] = useMutation(CREATE_USER, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createSeaLogsMember
            if (data.id > 0) {
                setOpenCreateMemberDialog(false)
                resetCrewMemberForm()
                setMembersList([
                    ...membersList,
                    {
                        label: data.firstName + ' ' + data.surname,
                        value: data.id,
                    },
                ])
                setVesselMembers([
                    ...vesselMembers,
                    {
                        label: data.firstName + ' ' + data.surname,
                        value: data.id,
                    },
                ])
                setError(false)
            }
        },
        onError: (error: any) => {
            console.error('createUser error', error.message)
            setError(error)
        },
    })

    const handleDateofBuildChange = (date: any) => {
        const dateOfBuild = new Date(date)
        setDateOfBuild(dateOfBuild)
    }

    const handleDisplayOnDashboard = (checked: boolean) => {
        setDisplayOnDashboard(checked)
    }

    const handleDisplayLogbookComments = (checked: boolean) => {
        setDisplayLogbookComments(checked)
    }

    const defaultVesselType = { label: 'Recreation', value: 'Recreation' }
    // Exclude SLALL when creating a new vessel
    const vesselTypes = [
        { label: 'SLALL', value: 'SLALL' },
        { label: 'Rescue Vessel', value: 'Rescue_Vessel' },
        { label: 'Tug Boat', value: 'Tug_Boat' },
        { label: 'Pilot Vessel', value: 'Pilot_Vessel' },
        { label: 'Recreation', value: 'Recreation' },
        { label: 'Passenger Ferry', value: 'Passenger_Ferry' },
        { label: 'Water Taxi', value: 'Water_Taxi' },
        { label: 'Sailing Vessel', value: 'Sailing_Vessel' },
        { label: 'Large Motor Yacht', value: 'Large_Motor_Yacht' },
        { label: 'JetBoat', value: 'JetBoat' },
    ].filter((type) => vesselId !== 0 || type.value !== 'SLALL')

    const handleSetInventoryCategory = (value: any) => {
        if (value.value === 'newCategory') {
            setOpenCreateInventoryCategoryDialog(true)
        }
        setCurrentData({
            ...currentData,
            inventory: {
                ...currentData.inventory,
                category: value.value,
            },
        })
    }

    const handleAddNewInventoryCategory = async () => {
        await queryAddInventoryCategory({
            variables: {
                input: {
                    name: (
                        document.getElementById(
                            'inventory-category-title',
                        ) as HTMLInputElement
                    ).value,
                    abbreviation: (
                        document.getElementById(
                            'inventory-category-abbreviation',
                        ) as HTMLInputElement
                    ).value,
                },
            },
        })
    }

    const [queryAddInventoryCategory] = useMutation(CREATE_INVENTORY_CATEGORY, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createInventoryCategory
            if (data.id > 0) {
                setOpenCreateInventoryCategoryDialog(false)
                setInventoryCategories([
                    ...inventoryCategories,
                    { label: data.name, value: data.id },
                ])
                setCurrentData({
                    ...currentData,
                    inventory: {
                        ...currentData.inventory,
                        category: data.id,
                    },
                })
                setSelectedInventoryCategory({
                    label: data.name,
                    value: data.id,
                })
            }
        },
        onError: (error: any) => {
            console.error('createInventoryCategory error', error)
        },
    })

    const handleDepartmentChange = (departments: any) => {
        setSelectedDepartments(departments.map((d: any) => d.value))
    }
    useEffect(() => {
        if (isLoading) {
            if (vesselId === 0) {
                // Initialize defaults for new vessel
                setSelectedVesselType(defaultVesselType)
                setCurrentData({
                    vesseltype: defaultVesselType,
                })
                setIsLoading(false)
            } else {
                loadVesselInfo()
                setIsLoading(false)
            }
        }
    }, [isLoading])

    function handleImageChange(e: any) {
        setImageLoader(true)
        e.preventDefault()
        if (e.target.files && e.target.files[0]) {
            for (let i = 0; i < e.target.files['length']; i++) {
                setFiles((prevState: any) => [...prevState, e.target.files[i]])
                uploadFile(e.target.files[i])
            }
        }
    }

    async function uploadFile(file: any) {
        const formData = new FormData()
        formData.append('FileData', file, file.name.replace(/\s/g, ''))
        try {
            const response = await fetch(
                process.env.API_BASE_URL + 'v2/upload',
                {
                    method: 'POST',
                    headers: {
                        Authorization:
                            'Bearer ' + localStorage.getItem('sl-jwt'),
                    },
                    body: formData,
                },
            )
            const data = await response.json()
            setUploadImagedUrl(data[0].location)
            setUploadImagedID(data[0].id)
            setImageLoader(false)
        } catch (err) {
            console.error(err)
            setImageLoader(false)
        }
    }

    const [getFileDetails, { data, loading }] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readFiles.nodes
            setUploadImagedUrl(process.env.FILE_BASE_URL + data[0].fileFilename)
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const handleVesselIcon = () => {
        if (vessel?.id) {
            queryUpdateVesselWithoutReload({
                variables: {
                    input: {
                        id: vessel?.id,
                        icon: vesselIcon
                            ? vesselIcon.replaceAll(' ', '-')
                            : null,
                        iconMode: vesselIconMode,
                        photoID:
                            vesselPhoto.length > 0 ? vesselPhoto[0].id : null,
                    },
                },
            })
        }
        setVessel({
            ...vessel,
            icon: vesselIcon ? vesselIcon.replaceAll(' ', '-') : null,
            iconMode: vesselIconMode,
            photoID: vesselPhoto.length > 0 ? vesselPhoto[0].id : null,
        })
        setOpenVesselImageDialog(false)
    }

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    useEffect(() => {
        if (members.length > 0 && !isEmpty(vessel)) {
            doSetMemberList(members)
        }
    }, [members, vessel])
    if (!permissions || !hasPermission('VIEW_VESSEL', permissions)) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    const isReadOnly =
        !permissions || !hasPermission('EDIT_VESSEL', permissions)

    return (
        <div className="space-y-6">
            {/* Vessel Details Card */}

            <ListHeader
                title={
                    vesselId > 0
                        ? `Updating Vessel: ${vessel?.title}`
                        : 'Creating new vessel'
                }
            />

            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Vessel Details</H4>
                    <P>
                        Record details such as the vessel's name, size,
                        registration, etc. Make sure everything listed is
                        correct and kept up-to-date. These fields can be
                        exported with reports for the likes of survey reports
                        and certification documents.
                    </P>
                </CardHeader>
                <CardContent className="space-y-8">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="space-y-5">
                            <div className="flex items-center space-x-2">
                                <Label
                                    htmlFor="display-dashboard"
                                    label="Display on dashboard"
                                    size="lg"
                                    leftContent={
                                        <Checkbox
                                            id="display-dashboard"
                                            checked={displayOnDashboard}
                                            onCheckedChange={
                                                handleDisplayOnDashboard
                                            }
                                            isRadioStyle
                                            disabled={isReadOnly}
                                            size="lg"
                                        />
                                    }
                                />
                            </div>
                            <div className="flex items-center space-x-2">
                                <Label
                                    htmlFor="display-logbook-comments"
                                    label="Display previous logbook comments"
                                    size="lg"
                                    leftContent={
                                        <Checkbox
                                            id="display-logbook-comments"
                                            checked={displayLogbookComments}
                                            onCheckedChange={
                                                handleDisplayLogbookComments
                                            }
                                            isRadioStyle
                                            disabled={isReadOnly}
                                            size="lg"
                                        />
                                    }
                                />
                            </div>
                        </div>
                        <div className="lg:col-span-2 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Label
                                    label="Vessel Title"
                                    htmlFor="vessel-title">
                                    <Input
                                        readOnly={isReadOnly}
                                        id="vessel-title"
                                        type="text"
                                        placeholder="Vessel Title"
                                        defaultValue={vessel?.title}
                                    />
                                </Label>
                                <Label
                                    label="Country of Operation"
                                    htmlFor="country-operation">
                                    <Combobox
                                        className="w-full"
                                        options={CountriesList}
                                        value={selectedCountry}
                                        onChange={(value: any) => {
                                            setCurrentData({
                                                ...currentData,
                                                countryofoperation: value,
                                            })
                                            setSelectedCountry(value)
                                        }}
                                        title="Country of Operation"
                                        placeholder="Country of Operation"
                                        multi={false}
                                    />
                                </Label>
                            </div>

                            <Label
                                label="Primary Harbour"
                                htmlFor="vessel-primaryHarbour">
                                <Input
                                    id="vessel-primaryHarbour"
                                    type="text"
                                    readOnly={isReadOnly}
                                    placeholder="Home port"
                                    defaultValue={
                                        vessel?.vesselSpecifics?.primaryHarbour
                                    }
                                />
                            </Label>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                                <Label
                                    label="Authority No."
                                    htmlFor="vessel-authNo">
                                    <Input
                                        id="vessel-authNo"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="(MNZ, AMSA)"
                                        defaultValue={vessel?.registration}
                                    />
                                </Label>
                                <Label
                                    label="Transit identifier"
                                    htmlFor="vessel-transitId">
                                    <Input
                                        id="vessel-transitId"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="(AIS)"
                                        defaultValue={vessel?.transitID}
                                    />
                                </Label>
                                <Label label="MMSI" htmlFor="vessel-mmsi">
                                    <Input
                                        id="vessel-mmsi"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="For marine traffic maps"
                                        defaultValue={vessel?.mmsi}
                                    />
                                </Label>
                                <Label
                                    label="Call sign"
                                    htmlFor="vessel-callSign">
                                    <Input
                                        id="vessel-callSign"
                                        type="text"
                                        readOnly={isReadOnly}
                                        placeholder="Call sign"
                                        defaultValue={vessel?.callSign}
                                    />
                                </Label>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                                <Label
                                    label="Vessel beam"
                                    htmlFor="vessel-beam">
                                    <Input
                                        id="vessel-beam"
                                        type="text"
                                        readOnly={isReadOnly}
                                        placeholder="Beam"
                                        defaultValue={
                                            vessel?.vesselSpecifics?.beam
                                        }
                                    />
                                </Label>
                                <Label
                                    label="Length overall"
                                    htmlFor="vessel-overallLength">
                                    <Input
                                        id="vessel-overallLength"
                                        type="text"
                                        readOnly={isReadOnly}
                                        placeholder="L.O.A"
                                        defaultValue={
                                            vessel?.vesselSpecifics
                                                ?.overallLength
                                        }
                                    />
                                </Label>
                                <Label
                                    label="Date of build"
                                    htmlFor="vessel-dateOfBuild">
                                    <DatePicker
                                        mode="single"
                                        onChange={handleDateofBuildChange}
                                        className="w-full"
                                        placeholder="D.O.B"
                                        disabled={isReadOnly}
                                        value={dateOfBuild}
                                    />
                                </Label>
                                <Label label="Draft" htmlFor="vessel-draft">
                                    <Input
                                        id="vessel-draft"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="Draft"
                                        defaultValue={
                                            vessel?.vesselSpecifics?.draft
                                        }
                                    />
                                </Label>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <Label
                                    label="Hull construction"
                                    htmlFor="vessel-hullConstruction">
                                    <Input
                                        id="vessel-hullConstruction"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="(Steel, fibreglass, carbon etc)"
                                        defaultValue={
                                            vessel?.vesselSpecifics
                                                ?.hullConstruction
                                        }
                                    />
                                </Label>
                                <Label
                                    label="Hull color"
                                    htmlFor="vessel-hullColor">
                                    <Input
                                        id="vessel-hullColor"
                                        readOnly={isReadOnly}
                                        type="text"
                                        placeholder="Color"
                                        defaultValue={
                                            vessel?.vesselSpecifics?.hullColor
                                        }
                                    />
                                </Label>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
            {/* Vessel Configuration Card */}
            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Vessel Configuration</H4>
                    <P>
                        Select a vessel type from the dropdown. We use this to
                        help with constructing your logbook configuration. If a
                        vessel type does not adequately describe your vessel,
                        contact support and we can add this for you.
                    </P>
                    <P>
                        Add engines, fuel and water tanks, and sullage
                        configuration for this vessel.
                    </P>
                </CardHeader>
                <CardContent className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <Label
                            label="Vessel Type"
                            required
                            htmlFor="vessel-type">
                            <Combobox
                                className="w-full"
                                options={vesselTypes}
                                value={selectedVesselType}
                                onChange={(value: any) => {
                                    setCurrentData({
                                        ...currentData,
                                        vesseltype: value,
                                    })
                                    setVessel({
                                        ...vessel,
                                        vesselType: value,
                                    })
                                    setSelectedVesselType(value)
                                }}
                                title="Vessel Type"
                                placeholder="Vessel Type"
                                required
                                multi={false}
                                disabled={
                                    !permissions ||
                                    !hasPermission('EDIT_VESSEL', permissions)
                                }
                            />
                        </Label>
                        <Label
                            label="Vessel type description"
                            htmlFor="vessel-description">
                            <Input
                                id="vessel-description"
                                readOnly={isReadOnly}
                                type="text"
                                placeholder="Vessel type description"
                                defaultValue={vessel?.vesselTypeDescription}
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div>
                            <Label htmlFor="vesselIcon">
                                Vessel Icon / Photo
                            </Label>
                            <div className="flex items-center gap-3">
                                {vesselIconMode === 'Icon' &&
                                !isEmpty(vessel?.icon) ? (
                                    <img
                                        className="w-16 h-16 object-cover rounded-full border"
                                        src={`/vessel-icons/${vessel.icon}.svg`}
                                        alt=""
                                    />
                                ) : vesselIconMode === 'Photo' &&
                                  vesselPhoto.length > 0 ? (
                                    <img
                                        className="w-16 h-16 object-cover rounded-full border"
                                        src={
                                            process.env.FILE_BASE_URL +
                                            vesselPhoto[0].fileFilename
                                        }
                                        alt=""
                                    />
                                ) : (
                                    <SealogsVesselsIcon className="w-16 h-16 rounded-full border" />
                                )}

                                <Button
                                    onClick={() =>
                                        permissions &&
                                        hasPermission(
                                            'EDIT_VESSEL',
                                            permissions,
                                        ) &&
                                        setOpenVesselImageDialog(true)
                                    }>
                                    Change
                                </Button>
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="vesselBanner">Banner Image</Label>
                            <div className="flex items-center gap-3">
                                <div className="w-32 h-16">
                                    {imageLoader ? (
                                        <div className="flex justify-center items-center h-full">
                                            <svg
                                                className="w-6 h-6 animate-spin"
                                                viewBox="0 0 100 101"
                                                fill="none">
                                                <path
                                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C0 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                    fill="currentColor"
                                                />
                                                <path
                                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                    fill="currentFill"
                                                />
                                            </svg>
                                        </div>
                                    ) : (
                                        <img
                                            className="w-full h-full object-cover border rounded"
                                            src={
                                                uploadImagedUrl ||
                                                '/sealogs-SeaLogs_hero.png'
                                            }
                                            alt=""
                                        />
                                    )}
                                </div>
                                <Label
                                    htmlFor="fileUpload"
                                    label="Upload"
                                    className="inline-flex items-center justify-center gap-[8.5px] whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0 border rounded-[6px] bg-primary border-primary text-background shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-background hover:text-accent-foreground hover:border-border h-11 px-5 py-3 will-change-transform will-change-width will-change-padding transform-gpu hover:transition-colors hover:ease-out hover:duration-300 cursor-pointer"
                                />
                                <Input
                                    type="file"
                                    id="fileUpload"
                                    readOnly={isReadOnly}
                                    className="hidden"
                                    onChange={handleImageChange}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                        <Label
                            label="Minimum required crew"
                            htmlFor="vessel-minCrew">
                            <Input
                                id="vessel-minCrew"
                                type="text"
                                placeholder="Minimum crew"
                                defaultValue={vessel?.minCrew}
                                readOnly={isReadOnly}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Please include the skipper/master in this
                                number. If a vessel can operate with 1 crew +
                                the master then this number should be 2
                            </p>
                        </Label>
                        <Label
                            label="Maximum passengers allowed"
                            htmlFor="vessel-maxPax">
                            <Input
                                id="vessel-maxPax"
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Maximum passengers"
                                defaultValue={vessel?.maxPax}
                            />
                        </Label>
                        <Label
                            label="Maximum people on board"
                            htmlFor="vessel-maxPOB">
                            <Input
                                id="vessel-maxPOB"
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Max P.O.B"
                                defaultValue={vessel?.maxPOB}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                This is the maximum people on board including
                                the skipper/master.
                            </p>
                        </Label>
                    </div>

                    <CheckFieldLabel
                        type="checkbox"
                        id="carries-dangerous-goods"
                        checked={
                            !!vessel?.vesselSpecifics?.carriesDangerousGoods
                        }
                        onCheckedChange={(checked) => {
                            setVessel({
                                ...vessel,
                                vesselSpecifics: {
                                    ...vessel.vesselSpecifics,
                                    carriesDangerousGoods: checked,
                                },
                            })
                        }}
                        variant="warning"
                        disabled={isReadOnly}
                        label="This vessel is able to carry legally classified dangerous goods"
                        className="w-fit"
                    />

                    <CheckFieldLabel
                        type="checkbox"
                        id="carries-vehicles"
                        checked={!!vessel?.vesselSpecifics?.carriesVehicles}
                        onCheckedChange={(checked) => {
                            setVessel({
                                ...vessel,
                                vesselSpecifics: {
                                    ...vessel.vesselSpecifics,
                                    carriesVehicles: checked,
                                },
                            })
                        }}
                        variant="warning"
                        disabled={isReadOnly}
                        label="This vessel is able to carry vehicles"
                        className="w-fit"
                    />
                </CardContent>
            </Card>
            {/* Systems Configuration Card */}
            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Systems Configuration</H4>
                    <P>
                        Configure engines, fuel tanks, water tanks, and sewage
                        systems for this vessel.
                    </P>
                </CardHeader>
                <CardContent className="space-y-8">
                    <div>
                        <Label className="text-base font-medium">
                            Engines / motors
                        </Label>
                        <div className="mt-2 space-y-2">
                            {vessel?.parentComponent_Components?.nodes.length >
                                0 &&
                                engineList && (
                                    <div className="flex flex-wrap gap-2">
                                        {vessel?.parentComponent_Components?.nodes
                                            .filter(
                                                (item: any) =>
                                                    item.basicComponent
                                                        .componentCategory ===
                                                    'Engine',
                                            )
                                            .map((item: any, index: number) => (
                                                <Button
                                                    key={index}
                                                    variant="outline"
                                                    onClick={() => {
                                                        setOpenEngineDialog(
                                                            true,
                                                        )
                                                        handleSetSelectedEngine(
                                                            item.basicComponent
                                                                .id,
                                                        )
                                                    }}>
                                                    {
                                                        engineList?.find(
                                                            (engine: any) =>
                                                                engine.id ==
                                                                item
                                                                    .basicComponent
                                                                    .id,
                                                        )?.title
                                                    }
                                                </Button>
                                            ))}
                                    </div>
                                )}
                            <Button
                                iconLeft={Plus}
                                variant="outline"
                                onClick={() => {
                                    setOpenEngineDialog(true)
                                    setSelectedEngine(0)
                                }}
                                disabled={isReadOnly}>
                                Add engine
                            </Button>
                        </div>
                    </div>

                    <div>
                        <Label className="text-base font-medium">
                            Fuel tanks
                        </Label>
                        <div className="space-y-2">
                            {vessel?.parentComponent_Components?.nodes.length >
                                0 &&
                                fuelTankList && (
                                    <div className="flex flex-wrap gap-2">
                                        {vessel?.parentComponent_Components?.nodes
                                            .filter(
                                                (item: any) =>
                                                    item.basicComponent
                                                        .componentCategory ===
                                                        'FuelTank' &&
                                                    fuelTankList?.find(
                                                        (fuelTank: any) =>
                                                            fuelTank.id ==
                                                            item.basicComponent
                                                                .id,
                                                    )?.id > 0,
                                            )
                                            .map((item: any, index: number) => (
                                                <Button
                                                    iconLeft={Plus}
                                                    key={index}
                                                    variant="outline"
                                                    onClick={() => {
                                                        setOpenFuelTankDialog(
                                                            true,
                                                        )
                                                        handleSetSelectedFuelTank(
                                                            item.basicComponent
                                                                .id,
                                                        )
                                                    }}>
                                                    {
                                                        fuelTankList?.find(
                                                            (fuelTank: any) =>
                                                                fuelTank.id ==
                                                                item
                                                                    .basicComponent
                                                                    .id,
                                                        )?.title
                                                    }
                                                </Button>
                                            ))}
                                    </div>
                                )}
                            <Button
                                iconLeft={Plus}
                                variant="outline"
                                onClick={() => {
                                    setOpenFuelTankDialog(true)
                                    setSelectedFuelTank(0)
                                }}
                                disabled={isReadOnly}>
                                Add Fuel Tank
                            </Button>
                        </div>
                    </div>

                    <div>
                        <Label className="text-base font-medium">
                            Water tanks
                        </Label>
                        <div className="space-y-2">
                            {vessel?.parentComponent_Components?.nodes.length >
                                0 &&
                                waterTankList && (
                                    <div className="flex flex-wrap gap-2">
                                        {vessel?.parentComponent_Components?.nodes
                                            .filter(
                                                (item: any) =>
                                                    item.basicComponent
                                                        .componentCategory ===
                                                    'WaterTank',
                                            )
                                            .map((item: any, index: number) => (
                                                <Button
                                                    iconLeft={Plus}
                                                    key={index}
                                                    variant="outline"
                                                    onClick={() => {
                                                        setOpenWaterTankDialog(
                                                            true,
                                                        )
                                                        handleSetSelectedWaterTank(
                                                            item.basicComponent
                                                                .id,
                                                        )
                                                    }}>
                                                    {
                                                        waterTankList?.find(
                                                            (waterTank: any) =>
                                                                waterTank.id ==
                                                                item
                                                                    .basicComponent
                                                                    .id,
                                                        )?.title
                                                    }
                                                </Button>
                                            ))}
                                    </div>
                                )}
                            <Button
                                iconLeft={Plus}
                                variant="outline"
                                onClick={() => {
                                    setOpenWaterTankDialog(true)
                                    setSelectedWaterTank(0)
                                }}
                                disabled={isReadOnly}>
                                Add Water Tank
                            </Button>
                        </div>
                    </div>

                    <div>
                        <Label className="text-base font-medium">
                            Sewage system
                        </Label>
                        <div className="space-y-2">
                            {vessel?.parentComponent_Components?.nodes.filter(
                                (item: any) =>
                                    item.basicComponent.componentCategory ===
                                    'SewageSystem',
                            ).length > 0 &&
                                sewageSystemList && (
                                    <div className="flex flex-wrap gap-2">
                                        {vessel?.parentComponent_Components?.nodes
                                            .filter(
                                                (item: any) =>
                                                    item.basicComponent
                                                        .componentCategory ===
                                                    'SewageSystem',
                                            )
                                            .map((item: any, index: number) => (
                                                <Button
                                                    key={index}
                                                    variant="outline"
                                                    onClick={() => {
                                                        setOpenSewageSystemDialog(
                                                            true,
                                                        )
                                                        handleSetSelectedSewageSystem(
                                                            item.basicComponent
                                                                .id,
                                                        )
                                                    }}>
                                                    {
                                                        sewageSystemList?.find(
                                                            (
                                                                sewageSystem: any,
                                                            ) =>
                                                                sewageSystem.id ==
                                                                item
                                                                    .basicComponent
                                                                    .id,
                                                        )?.title
                                                    }
                                                </Button>
                                            ))}
                                    </div>
                                )}
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setOpenSewageSystemDialog(true)
                                    setSelectedSewageSystem(0)
                                }}
                                disabled={isReadOnly}>
                                Add Sewage System
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
            {/* Inventory Card */}
            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Inventory</H4>
                    <P>
                        Attach inventory items to this vessel. Record uses,
                        create maintenance tasks (one-off and recurring), add
                        manuals and documentation.
                    </P>
                </CardHeader>
                <CardContent>
                    {inventoryList && (
                        <Combobox
                            className="w-full"
                            options={inventoryList}
                            value={vesselInventories}
                            onChange={handleInventoryChange}
                            title="Select Inventory"
                            placeholder="Select Inventory"
                            multi={true}
                        />
                    )}
                </CardContent>
            </Card>
            {/* Crew Card */}
            <Card className="mx-2.5">
                <CardHeader>
                    <H4>Crew</H4>
                    <P>
                        Add or update crew members to this vessel crew list.
                        Linking your crew here will make it faster to add crew
                        to your logbook entries and will help you track the
                        status of your crew's training and qualifications.
                    </P>
                </CardHeader>
                <CardContent>
                    {membersList && (
                        <Combobox
                            className="w-full"
                            options={membersList}
                            value={vesselMembers}
                            onChange={handleMemberChange}
                            title="Select Crew Members"
                            placeholder="Select Crew Members"
                            multi={true}
                        />
                    )}
                </CardContent>
            </Card>
            {/* Department Card */}
            {localStorage.getItem('useDepartment') === 'true' && (
                <Card className="mx-2.5">
                    <CardHeader>
                        <H4>Department</H4>
                        <P>Add or update departments to this vessel.</P>
                    </CardHeader>
                    <CardContent>
                        {departmentList && (
                            <DepartmentMultiSelectDropdown
                                value={selectedDepartments}
                                onChange={handleDepartmentChange}
                                allDepartments={departmentList}
                            />
                        )}
                    </CardContent>
                </Card>
            )}
            {/* Footer with action buttons */}
            <FooterWrapper>
                <Button
                    iconLeft={ArrowLeft}
                    variant="back"
                    onClick={() =>
                        vesselId > 0
                            ? router.push(`/vessel/info?id=${vesselId}`)
                            : router.push('/vessel')
                    }>
                    Cancel
                </Button>
                {+vesselId > 0 && (
                    <Button
                        variant="destructive"
                        onClick={() => setOpenArchiveVesselDialog(true)}>
                        {responsiveLabel('Archive', 'Archive vessel')}
                    </Button>
                )}

                <Button onClick={() => handleUpdate(0)}>
                    {`${vesselId > 0 ? 'Update' : 'Create'} ${responsiveLabel('', 'vessel')}`}
                </Button>
            </FooterWrapper>
            {/* All AlertDialogNew components */}
            <AlertDialogNew
                openDialog={openEngineDialog}
                setOpenDialog={setOpenEngineDialog}
                handleCreate={handleAddNewEngine}
                title={`${selectedEngine > 0 ? 'Update Engine' : 'New Engine'}`}
                size="xl"
                actionText={`${selectedEngine > 0 ? 'Update Engine' : 'Add Engine'}`}>
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Title" htmlFor="engine-title">
                            <Input
                                id={`engine-title`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Title"
                                defaultValue={
                                    selectedEngine > 0
                                        ? engineList
                                              .filter(
                                                  (engine: any) =>
                                                      engine.id ==
                                                      selectedEngine,
                                              )
                                              .map(
                                                  (engine: any) => engine.title,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                        <Label
                            label="Identifier (abbreviation)"
                            htmlFor="engine-identified">
                            <Input
                                id={`engine-identified`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Identifier (abbreviation)"
                                defaultValue={
                                    selectedEngine > 0
                                        ? engineList
                                              .filter(
                                                  (engine: any) =>
                                                      engine.id ==
                                                      selectedEngine,
                                              )
                                              .map(
                                                  (engine: any) =>
                                                      engine?.identifier,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Combobox
                            className="w-full"
                            label="Type"
                            buttonClassName="w-auto min-w-auto"
                            options={[
                                { value: 'Main', label: 'Main' },
                                { value: 'Auxiliary', label: 'Auxiliary' },
                                { value: 'Generator', label: 'Generator' },
                            ]}
                            value={(() => {
                                if (currentData.engine?.type) {
                                    return {
                                        value: currentData.engine.type,
                                        label: currentData.engine.type,
                                    }
                                }
                                return null
                            })()}
                            onChange={(value: any) => {
                                setCurrentData({
                                    ...currentData,
                                    engine: {
                                        ...currentData.engine,
                                        type: value?.value || null,
                                    },
                                })
                                //setSelectedEngineType(value)
                            }}
                            title={'Type'}
                            placeholder={'Type'}
                            multi={false}
                        />

                        <Combobox
                            className="w-full"
                            label="Drive type"
                            buttonClassName="w-auto min-w-auto"
                            options={[
                                {
                                    value: 'Stern drive',
                                    label: 'Stern drive',
                                },
                                { value: 'Jet', label: 'Jet' },
                                { value: 'Outboard', label: 'Outboard' },
                                { value: 'Inboard', label: 'Inboard' },
                            ]}
                            value={(() => {
                                if (currentData.engine?.driveType) {
                                    return {
                                        value: currentData.engine.driveType,
                                        label: currentData.engine.driveType,
                                    }
                                }
                                return null
                            })()}
                            onChange={(value: any) =>
                                setCurrentData({
                                    ...currentData,
                                    engine: {
                                        ...currentData.engine,
                                        driveType: value?.value || null,
                                    },
                                })
                            }
                            title={'Drive type'}
                            placeholder={'Drive type'}
                            multi={false}
                        />
                    </div>
                    <Combobox
                        label="Position on vessel"
                        className="w-full"
                        buttonClassName="w-auto min-w-auto"
                        options={[
                            { value: 'Port', label: 'Port' },
                            { value: 'Starboard', label: 'Starboard' },
                            { value: 'Centre', label: 'Centre' },
                        ]}
                        value={(() => {
                            if (currentData.engine?.positionOnVessel) {
                                return {
                                    value: currentData.engine.positionOnVessel,
                                    label: currentData.engine.positionOnVessel,
                                }
                            }
                            return null
                        })()}
                        onChange={(value: any) =>
                            setCurrentData({
                                ...currentData,
                                engine: {
                                    ...currentData.engine,
                                    positionOnVessel: value?.value || null,
                                },
                            })
                        }
                        title={'Position on vessel'}
                        placeholder={'Position on vessel'}
                        multi={false}
                    />
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Engine make" htmlFor="engine-make">
                            <Input
                                id={`engine-make`}
                                readOnly={isReadOnly}
                                type="text"
                                placeholder="Make"
                                defaultValue={
                                    selectedEngine > 0
                                        ? engineList.find(
                                              (engine: any) =>
                                                  engine.id == selectedEngine,
                                          )?.make
                                        : ''
                                }
                            />
                        </Label>
                        <Label label="and model" htmlFor="engine-model">
                            <Input
                                id={`engine-model`}
                                readOnly={isReadOnly}
                                type="text"
                                placeholder="Model"
                                defaultValue={
                                    selectedEngine > 0
                                        ? engineList.find(
                                              (engine: any) =>
                                                  engine.id == selectedEngine,
                                          )?.model
                                        : ''
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Engine kilowatts" htmlFor="engine-kW">
                            <Input
                                id={`engine-kW`}
                                type="number"
                                readOnly={isReadOnly}
                                placeholder="kW"
                                defaultValue={
                                    selectedEngine > 0 &&
                                    engineList
                                        .filter(
                                            (engine: any) =>
                                                engine.id == selectedEngine,
                                        )
                                        .map((engine: any) => engine.kW)
                                }
                            />
                        </Label>
                        <Label
                            label="Genset kilovolt-amperes"
                            htmlFor="engine-kVA">
                            <Input
                                id={`engine-kVA`}
                                type="number"
                                placeholder="kVA"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedEngine > 0 &&
                                    engineList
                                        .filter(
                                            (engine: any) =>
                                                engine.id == selectedEngine,
                                        )
                                        .map((engine: any) => engine.kVA)
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label
                            label="Current engine hours"
                            htmlFor="engine-hours">
                            <Input
                                id={`engine-hours`}
                                type="number"
                                readOnly={isReadOnly}
                                placeholder="Current hours"
                                defaultValue={
                                    selectedEngine > 0 &&
                                    engineList
                                        .filter(
                                            (engine: any) =>
                                                engine.id == selectedEngine,
                                        )
                                        .map(
                                            (engine: any) =>
                                                engine?.currentHours,
                                        )
                                }
                            />
                        </Label>
                        <Label label="Max engine powers" htmlFor="engine-power">
                            <Input
                                id={`engine-power`}
                                type="number"
                                placeholder="Max power"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedEngine > 0 &&
                                    engineList
                                        .filter(
                                            (engine: any) =>
                                                engine.id == selectedEngine,
                                        )
                                        .map((engine: any) => engine?.maxPower)
                                }
                            />
                        </Label>
                    </div>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openFuelTankDialog}
                setOpenDialog={setOpenFuelTankDialog}
                handleCreate={handleAddNewFuelTank}
                title={`${selectedFuelTank > 0 ? 'Update Fuel Tank' : 'New Fuel Tank'}`}
                handleDestructiveAction={() =>
                    handleDeleteFuelTank(selectedFuelTank)
                }
                destructiveActionText={
                    selectedFuelTank > 0 ? 'Delete Fuel Tank' : ''
                }
                size="xl"
                actionText={`${selectedFuelTank > 0 ? 'Update Fuel Tank' : 'Add Fuel Tank'}`}>
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Title" htmlFor="fuel-tank-title">
                            <Input
                                id={`fuel-tank-title`}
                                type="text"
                                placeholder="Main Fuel Tank"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedFuelTank > 0
                                        ? fuelTankList
                                              .filter(
                                                  (fuelTank: any) =>
                                                      fuelTank.id ==
                                                      selectedFuelTank,
                                              )
                                              .map(
                                                  (fuelTank: any) =>
                                                      fuelTank.title,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                        <Label
                            label="Identifier"
                            htmlFor="fuel-tank-identified">
                            <Input
                                id={`fuel-tank-identified`}
                                type="text"
                                placeholder="FT-001"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedFuelTank > 0
                                        ? fuelTankList
                                              .filter(
                                                  (fuelTank: any) =>
                                                      fuelTank.id ==
                                                      selectedFuelTank,
                                              )
                                              .map(
                                                  (fuelTank: any) =>
                                                      fuelTank.identifier,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        {/* safeFuelCapacity */}
                        <Label label="Safe fuel capacity">
                            <Input
                                id={`fuel-tank-safeCapacity`}
                                type="number"
                                readOnly={isReadOnly}
                                className={` grow`}
                                placeholder="Safe fuel level"
                                defaultValue={
                                    selectedFuelTank > 0
                                        ? fuelTankList
                                              .filter(
                                                  (fuelTank: any) =>
                                                      fuelTank.id ==
                                                          selectedFuelTank &&
                                                      fuelTank.safeFuelCapacity >
                                                          0,
                                              )
                                              .map(
                                                  (fuelTank: any) =>
                                                      fuelTank.safeFuelCapacity,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                        <Label label="Maximum fuel capacity">
                            <Input
                                id={`fuel-tank-capacity`}
                                type="number"
                                className={` grow`}
                                readOnly={isReadOnly}
                                placeholder="Max fuel level"
                                defaultValue={
                                    selectedFuelTank > 0
                                        ? fuelTankList
                                              .filter(
                                                  (fuelTank: any) =>
                                                      fuelTank.id ==
                                                          selectedFuelTank &&
                                                      fuelTank.capacity > 0,
                                              )
                                              .map(
                                                  (fuelTank: any) =>
                                                      fuelTank.capacity,
                                              )
                                        : ''
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Combobox
                            className="w-full"
                            label="Fuel type"
                            options={[
                                {
                                    value: 'Unleaded 91',
                                    label: 'Unleaded 91',
                                },
                                {
                                    value: 'Unleaded 95',
                                    label: 'Unleaded 95',
                                },
                                { value: 'Diesel', label: 'Diesel' },
                                { value: 'Other', label: 'Other' },
                            ]}
                            value={(() => {
                                if (currentData.fuelTank?.fuelType) {
                                    return {
                                        value: currentData.fuelTank.fuelType,
                                        label: currentData.fuelTank.fuelType,
                                    }
                                }
                                return null
                            })()}
                            onChange={(value: any) =>
                                setCurrentData({
                                    ...currentData,
                                    fuelTank: {
                                        ...currentData.fuelTank,
                                        fuelType:
                                            trim(value?.value) === ''
                                                ? null
                                                : value?.value || null,
                                    },
                                })
                            }
                            title={'Select'}
                            placeholder={'Select'}
                        />
                        <Label
                            label="Current fuel level"
                            htmlFor="fuel-tank-currentLevel">
                            <Input
                                type="number"
                                className={` grow`}
                                readOnly={isReadOnly}
                                value={currentData.fuelTank?.currentLevel}
                                onChange={(e) =>
                                    setCurrentData({
                                        ...currentData,
                                        fuelTank: {
                                            ...currentData.fuelTank,
                                            currentLevel: e.target.value,
                                        },
                                    })
                                }
                            />
                        </Label>
                    </div>
                    {/* <div>
                            DR: I commented this out for the timebeing while I confirm with Kylie
                            <Label className="mb-1 ">
                                Upload a Conversion CSV file
                            </Label>
                            <Input
                                id={`fuel-tank-csv`}
                                type="file"
                                placeholder="Conversion CSV file"
                            />
                        </div> */}
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openWaterTankDialog}
                setOpenDialog={setOpenWaterTankDialog}
                handleCreate={handleAddNewWaterTank}
                title={`${selectedWaterTank > 0 ? 'Update Water Tank' : 'New Water Tank'}`}
                size="xl"
                actionText={`${selectedWaterTank > 0 ? 'Update Water Tank' : 'Add Water Tank'}`}>
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Title" htmlFor="water-tank-title">
                            <Input
                                id={`water-tank-title`}
                                type="text"
                                placeholder="Fresh Water Tank"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedWaterTank > 0
                                        ? waterTankList
                                              .filter(
                                                  (waterTank: any) =>
                                                      waterTank.id ==
                                                      selectedWaterTank,
                                              )
                                              .map(
                                                  (waterTank: any) =>
                                                      waterTank.title,
                                              )
                                        : null
                                }
                            />
                        </Label>
                        <Label
                            label="Identifier"
                            htmlFor="water-tank-identified">
                            <Input
                                id={`water-tank-identified`}
                                type="text"
                                placeholder="WT-001"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedWaterTank > 0
                                        ? waterTankList
                                              .filter(
                                                  (waterTank: any) =>
                                                      waterTank.id ==
                                                      selectedWaterTank,
                                              )
                                              .map(
                                                  (waterTank: any) =>
                                                      waterTank.identifier,
                                              )
                                        : null
                                }
                            />
                        </Label>
                    </div>
                    <Label
                        label="Fresh Water Capacity"
                        htmlFor="water-tank-capacity">
                        <Input
                            id={`water-tank-capacity`}
                            type="number"
                            readOnly={isReadOnly}
                            placeholder="500"
                            defaultValue={
                                selectedWaterTank > 0
                                    ? waterTankList
                                          .filter(
                                              (waterTank: any) =>
                                                  waterTank.id ==
                                                      selectedWaterTank &&
                                                  waterTank.capacity > 0,
                                          )
                                          .map(
                                              (waterTank: any) =>
                                                  waterTank.capacity,
                                          )
                                    : null
                            }
                        />
                    </Label>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openSewageSystemDialog}
                setOpenDialog={setOpenSewageSystemDialog}
                handleCreate={handleAddNewSewageSystem}
                size="xl"
                title={`${selectedSewageSystem > 0 ? 'Update Sewage System' : 'New Sewage System'}`}
                actionText={`${selectedSewageSystem > 0 ? 'Update Sewage System' : 'Add Sewage System'}`}>
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Title" htmlFor="sewage-system-title">
                            <Input
                                id={`sewage-system-title`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Main Sewage System"
                                defaultValue={
                                    selectedSewageSystem > 0
                                        ? sewageSystemList
                                              .filter(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.id ==
                                                      selectedSewageSystem,
                                              )
                                              .map(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.title,
                                              )
                                        : null
                                }
                            />
                        </Label>
                        <Label
                            label="Identifier"
                            htmlFor="sewage-system-identified">
                            <Input
                                id={`sewage-system-identified`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="SS-001"
                                defaultValue={
                                    selectedSewageSystem > 0
                                        ? sewageSystemList
                                              .filter(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.id ==
                                                      selectedSewageSystem,
                                              )
                                              .map(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.identifier,
                                              )
                                        : null
                                }
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label
                            label="Number of Tanks"
                            htmlFor="sewage-system-numberOfTanks">
                            <Input
                                id={`sewage-system-numberOfTanks`}
                                type="number"
                                placeholder="2"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedSewageSystem > 0
                                        ? sewageSystemList
                                              .filter(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.id ==
                                                          selectedSewageSystem &&
                                                      sewageSystem.numberOfTanks >
                                                          0,
                                              )
                                              .map(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.numberOfTanks,
                                              )
                                        : null
                                }
                            />
                        </Label>
                        <Label
                            label="Capacity"
                            htmlFor="sewage-system-capacity">
                            <Input
                                id={`sewage-system-capacity`}
                                type="number"
                                placeholder="100"
                                readOnly={isReadOnly}
                                defaultValue={
                                    selectedSewageSystem > 0
                                        ? sewageSystemList
                                              .filter(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.id ==
                                                          selectedSewageSystem &&
                                                      sewageSystem.capacity > 0,
                                              )
                                              .map(
                                                  (sewageSystem: any) =>
                                                      sewageSystem.capacity,
                                              )
                                        : null
                                }
                            />
                        </Label>
                    </div>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCreateInventoryDialog}
                setOpenDialog={setOpenCreateInventoryDialog}
                handleCreate={handleAddNewInventory}
                title="Add Inventory"
                size="xl"
                actionText="Add Inventory">
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Title" htmlFor="inventory-item">
                            <Input
                                id={`inventory-item`}
                                type="text"
                                placeholder="Life Jacket"
                                readOnly={isReadOnly}
                            />
                        </Label>
                        <Label label="Product Code" htmlFor="inventory-code">
                            <Input
                                id={`inventory-code`}
                                type="text"
                                placeholder="LJ-001"
                                readOnly={isReadOnly}
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Quantity" htmlFor="inventory-qty">
                            <Input
                                id={`inventory-qty`}
                                type="number"
                                readOnly={isReadOnly}
                                placeholder="10"
                            />
                        </Label>
                        <Label label="Costing details" htmlFor="inventory-cost">
                            <Input
                                id={`inventory-cost`}
                                readOnly={isReadOnly}
                                type="text"
                                placeholder="$50.00 each"
                            />
                        </Label>
                    </div>
                    {/* inventoryCategories */}
                    <Combobox
                        options={inventoryCategories}
                        isDisabled={isReadOnly}
                        label="Category"
                        placeholder="Select Category"
                        className="w-full"
                        value={selectedInventoryCategory}
                        onChange={handleSetInventoryCategory}
                    />
                    <Label label="Location" htmlFor="inventory-location">
                        <Input
                            id={`inventory-location`}
                            type="text"
                            placeholder="Port Storage Locker"
                            readOnly={isReadOnly}
                        />
                    </Label>
                    <Textarea
                        id={`inventory-short-description`}
                        placeholder="Description"
                        readOnly={isReadOnly}
                    />
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCreateMemberDialog}
                setOpenDialog={(open) => {
                    setOpenCreateMemberDialog(open)
                    if (!open) resetCrewMemberForm()
                }}
                handleCreate={handleAddNewMember}
                title="Add Crew Member"
                size="xl"
                actionText="Add Crew Member">
                <div className="space-y-5">
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="First Name" htmlFor="crew-firstName">
                            <Input
                                id={`crew-firstName`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="John"
                            />
                        </Label>
                        <Label label="Surname" htmlFor="crew-surname">
                            <Input
                                id={`crew-surname`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="Smith"
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Username" htmlFor="crew-username">
                            <Input
                                id={`crew-username`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="jsmith"
                            />
                        </Label>
                        <Label label="Password" htmlFor="crew-password">
                            <Input
                                id={`crew-password`}
                                type="password"
                                readOnly={isReadOnly}
                                placeholder="••••••••"
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Email" htmlFor="crew-email">
                            <Input
                                id={`crew-email`}
                                type="email"
                                readOnly={isReadOnly}
                                placeholder="<EMAIL>"
                            />
                        </Label>
                        <Label label="Phone Number" htmlFor="crew-phoneNumber">
                            <Input
                                id={`crew-phoneNumber`}
                                type="text"
                                readOnly={isReadOnly}
                                placeholder="+****************"
                            />
                        </Label>
                    </div>
                    <div className="grid grid-cols-1 tablet-sm:grid-cols-2 gap-5">
                        <Label label="Primary Duty" htmlFor="primaryDuty">
                            <CrewDutyDropdown
                                onChange={handleDutyChange}
                                crewDutyID={crewDutyID}
                            />
                        </Label>
                        <Label label="User Role" htmlFor="userRole">
                            <Combobox
                                placeholder="Select roles"
                                onChange={handleGroupChange}
                                value={userGroups}
                                options={groups}
                            />
                        </Label>
                    </div>
                </div>
                {error && <div className="">{error.message}</div>}
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCreateInventoryCategoryDialog}
                setOpenDialog={setOpenCreateInventoryCategoryDialog}
                title="Create Inventory Category"
                actionText="Create Category"
                handleCreate={handleAddNewInventoryCategory}>
                <div className="space-y-8">
                    <Label label="Title" htmlFor="inventory-category-title">
                        <Input
                            id={`inventory-category-title`}
                            type="text"
                            readOnly={isReadOnly}
                            placeholder="Safety Equipment"
                        />
                    </Label>

                    <Label
                        label="Abbreviation"
                        htmlFor="inventory-category-abbreviation">
                        <Input
                            id={`inventory-category-abbreviation`}
                            type="text"
                            readOnly={isReadOnly}
                            placeholder="SAFE"
                        />
                    </Label>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openArchiveVesselDialog}
                setOpenDialog={setOpenArchiveVesselDialog}
                handleCreate={handleDelete}
                title="Archive Vessel"
                actionText="Archive Vessel">
                <div className="my-4 flex items-center">
                    Are you sure you want to archive this vessel?
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openVesselImageDialog}
                setOpenDialog={setOpenVesselImageDialog}
                handleCreate={handleVesselIcon}
                title="Select Vessel Icon / Upload a Photo"
                actionText="Save">
                <Tabs
                    value={vesselIconMode || 'Icon'}
                    onValueChange={setVesselIconMode}
                    className="w-full">
                    <TabsList>
                        <TabsTrigger value="Icon">Icon</TabsTrigger>
                        <TabsTrigger value="Photo">Photo</TabsTrigger>
                    </TabsList>
                    <TabsContent value="Icon" className="mt-4 space-y-2.5">
                        {vesselIconList.map((icon: any, index: number) => {
                            const isSelected = vesselIcon === icon.title
                            return (
                                <Button
                                    key={index}
                                    variant={isSelected ? 'primary' : 'outline'}
                                    className={`w-full h-fit py-2.5 justify-start ${
                                        isSelected
                                            ? 'bg-primary-foreground text-primary border-primary'
                                            : ''
                                    }`}
                                    iconLeft={
                                        <img
                                            src={`/vessel-icons/${icon.icon}`}
                                            alt="icon"
                                            className="size-12"
                                        />
                                    }
                                    onClick={() => setVesselIcon(icon.title)}>
                                    <span className="uppercase">
                                        {icon.title}
                                    </span>
                                </Button>
                            )
                        })}
                    </TabsContent>
                    <TabsContent value="Photo" className="mt-4">
                        <div className="flex w-full gap-5">
                            {vesselPhoto.length > 0 &&
                                vesselPhoto.map((photo: any, index: number) => (
                                    <div
                                        key={index}
                                        className="flex items-center justify-between w-1/2">
                                        <div className="flex items-center gap-5 w-full">
                                            <img
                                                src={`${process.env.FILE_BASE_URL + photo.fileFilename}`}
                                                alt="icon"
                                                className="h-56 object-cover w-full"
                                            />
                                        </div>
                                    </div>
                                ))}
                            <div
                                className={`${vesselPhoto.length > 0 ? 'w-1/2' : 'w-full'}`}>
                                <FileUpload
                                    setDocuments={setVesselPhoto}
                                    text="Vessel Photo"
                                    documents={vesselPhoto}
                                    multipleUpload={false}
                                />
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </AlertDialogNew>
        </div>
    )
}
